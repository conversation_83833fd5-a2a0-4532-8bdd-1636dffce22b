{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\home\\homeBox.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\home\\homeBox.vue", "mtime": 1756455523062}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA6MA;AACA;AACA;AACA;AAEA;EACAA,iBADA;EAEAC;IACAC,YADA;IAEAC,QAFA;IAGAC;EAHA,CAFA;;EAOAC;IACA;MACAC,eADA;MAEA;MACAC,yBACA;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CADA,EAEA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,CAFA,CAHA;MAOA;MACAC,mBACA;QAAAZ;QAAAU;MAAA,CADA,EAEA;QAAAV;QAAAU;MAAA,CAFA,EAGA;QAAAV;QAAAU;MAAA,CAHA,EAIA;QAAAV;QAAAU;MAAA,CAJA,EAKA;QAAAV;QAAAU;MAAA,CALA,EAMA;QAAAV;QAAAU;MAAA,CANA,EAOA;QAAAV;QAAAU;MAAA,CAPA,EAQA;QAAAV;QAAAU;MAAA,CARA,EASA;QAAAV;QAAAU;MAAA,CATA,EAUA;QAAAV;QAAAU;MAAA,CAVA,CARA;MAoBA;MACAG,wBACA;QAAAL;QAAAC;QAAAC;QAAAI;QAAAH;MAAA,CADA,EAEA;QAAAH;QAAAC;QAAAC;QAAAI;QAAAH;MAAA,CAFA,EAGA;QAAAH;QAAAC;QAAAC;QAAAI;QAAAH;MAAA,CAHA,CArBA;MA0BA;MACAI,oBACA;QAAAf;QAAAU;MAAA,CADA,EAEA;QAAAV;QAAAU;MAAA,CAFA,EAGA;QAAAV;QAAAU;MAAA,CAHA,EAIA;QAAAV;QAAAU;MAAA,CAJA,EAKA;QAAAV;QAAAU;MAAA,CALA,EAMA;QAAAV;QAAAU;MAAA,CANA,EAOA;QAAAV;QAAAU;MAAA,CAPA,EAQA;QAAAV;QAAAU;MAAA,CARA,EASA;QAAAV;QAAAU;MAAA,CATA,EAUA;QAAAV;QAAAU;MAAA,CAVA,EAWA;QAAAV;QAAAU;MAAA,CAXA,EAYA;QAAAV;QAAAU;MAAA,CAZA,CA3BA;MAyCAM,yBAzCA;MA0CA;MACAC,mBACA;QACAC,KADA;QAEAC,uCAFA;QAGAC;MAHA,CADA,EAMA;QACAF,KADA;QAEAC,qCAFA;QAGAC;MAHA,CANA,EAWA;QACAF,KADA;QAEAC,0BAFA;QAGAC;MAHA,CAXA,EAgBA;QACAF,KADA;QAEAC,wBAFA;QAGAC;MAHA,CAhBA,EAqBA;QACAF,KADA;QAEAC,4BAFA;QAGAC;MAHA,CArBA,EA0BA;QACAF,KADA;QAEAC,0BAFA;QAGAC;MAHA,CA1BA,CA3CA;MA2EA;MACAC,kBACA;QAAArB;QAAAsB;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CADA,EAEA;QAAA3B;QAAAsB;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CAFA,EAGA;QAAA3B;QAAAsB;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CAHA,EAIA;QAAA3B;QAAAsB;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CAJA,EAKA;QAAA3B;QAAAsB;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CALA,EAMA;QAAA3B;QAAAsB;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CANA,EAOA;QAAA3B;QAAAsB;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CAPA,EAQA;QAAA3B;QAAAsB;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CARA,CA5EA;MAsFA;MACAC;QACAC;UACAC,UADA;UAEAC;QAFA,CADA;QAKAC;UACAF,UADA;UAEAC;QAFA,CALA;QASAE;MATA,CAvFA;MAkGA;MACAC,2BACA;QAAAlC;QAAAU;MAAA,CADA,EAEA;QAAAV;QAAAU;MAAA,CAFA,EAGA;QAAAV;QAAAU;MAAA,CAHA,EAIA;QAAAV;QAAAU;MAAA,CAJA,CAnGA;MAyGA;MACAyB;QACAC,aACA;UAAApC;UAAAU;UAAAI;QAAA,CADA,EAEA;UAAAd;UAAAU;UAAAI;QAAA,CAFA,EAGA;UAAAd;UAAAU;UAAAI;QAAA,CAHA,CADA;QAMAuB,YACA,cADA,EAEA,0BAFA,EAGA,gBAHA;MANA,CA1GA;MAsHAC,UACA;QAAAtC;QAAAU;QAAA6B;QAAAC;MAAA,CADA,EAEA;QAAAxC;QAAAU;QAAA6B;QAAAC;MAAA,CAFA,EAGA;QAAAxC;QAAAU;QAAA6B;QAAAC;MAAA,CAHA,EAIA;QAAAxC;QAAAU;QAAA6B;QAAAC;MAAA,CAJA,EAKA;QAAAxC;QAAAU;QAAA6B;QAAAC;MAAA,CALA,EAMA;QAAAxC;QAAAU;QAAA6B;QAAAC;MAAA,CANA,EAOA;QAAAxC;QAAAU;QAAA6B;QAAAC;MAAA,CAPA,EAQA;QAAAxC;QAAAU;QAAA6B;QAAAC;MAAA,CARA,EASA;QAAAxC;QAAAU;QAAA6B;QAAAC;MAAA,CATA,EAUA;QAAAxC;QAAAU;QAAA6B;QAAAC;MAAA,CAVA,CAtHA;MAkIAD,mEAlIA;MAmIAE;IAnIA;EAqIA,CA7IA;;EA8IAC,YA9IA;;EAgJAC;IACA;IACA;IACA;EACA,CApJA;;EAqJAC;IACA;MACAC;IACA;EACA,CAzJA;;EA0JAC;IACAC;MACA;QAAAC;QAAAC;MAAA;MACAD;MACAC;IACA,CALA;;IAMAC;MACA;MACA;QACAC,eADA;QAEAC,gBAFA;QAGAC,cAHA;QAIAC,eAJA;QAKAC,iBALA;QAMAC;MANA;IAQA,CAhBA;;IAiBAC;MACA;QACA;MACA,CAFA,MAEA;QACA;MACA;;MACA;IACA,CAxBA;;IAyBAC;MACAC,6BADA,CAEA;MACA;IACA,CA7BA;;IA8BA;IACAC;MACA;QAAAC;QAAAC;UAAAC;QAAA;MAAA;IACA,CAjCA;;IAkCA;IACAC;MACA;QAAAH;QAAAC;UAAAC;QAAA;MAAA;IACA,CArCA;;IAsCA;IACAE;MACA;QAAAJ;QAAAC;UAAAC;QAAA;MAAA;IACA,CAzCA;;IA0CA;IACAG;MACA;QAAAL;QAAAC;UAAAC;QAAA;MAAA;IACA,CA7CA;;IA8CA;IACAI;MACA;QAAAN;QAAAC;UAAAC;QAAA;MAAA;IACA;;EAjDA;AA1JA", "names": ["name", "components", "MapComponent", "<PERSON><PERSON><PERSON>", "BarScrollChart", "data", "currentTime", "committeeStatisticsNum", "icon", "label", "value", "color", "committeeBarData", "proposalStatisticsNum", "unit", "proposalChartData", "proposalChartName", "workDynamicsData", "id", "title", "date", "performanceData", "meeting", "proposal", "opinion", "suggestion", "reading", "training", "socialData", "memberSubmit", "count", "adopted", "unitSubmit", "total", "conferenceActivitiesData", "discussionsData", "statistics", "hotTopics", "mapData", "areaId", "adcode", "areaName", "computed", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "initScreen", "calcRate", "windowDraw", "updateTime", "year", "month", "day", "hour", "minute", "second", "getItemClass", "handleRegionClick", "console", "handleCommitteeClick", "path", "query", "route", "handleProposalClick", "handlePerformanceClick", "handleNetWorkClick", "handlePublicOpinionClick"], "sourceRoot": "src/views/smartBrainLargeScreen/home", "sources": ["homeBox.vue"], "sourcesContent": ["<template>\n  <div class=\"big-screen\" ref=\"bigScreen\">\n    <div class=\"screen-header\">\n      <div class=\"header-left\">\n        <span class=\"date-time\">{{ currentTime }}</span>\n        <span class=\"weather\">晴 24℃ 东南风</span>\n      </div>\n      <div class=\"header-center\">\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\n      </div>\n      <div class=\"header-right\"></div>\n    </div>\n    <div class=\"screen-content\">\n      <div class=\"left-panel\">\n        <!-- 委员统计 -->\n        <div class=\"committee_statistics\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\" @click=\"handleCommitteeClick\">委员统计</span>\n            <span class=\"header_text_right\">十二届二次</span>\n          </div>\n          <div class=\"committee_statistics_content\">\n            <div class=\"committee_statistics_num\">\n              <div v-for=\"(item, index) in committeeStatisticsNum\" :key=\"index\" class=\"num_box\">\n                <img :src=\"item.icon\" alt=\"\" class=\"num_icon\">\n                <div>\n                  <div class=\"num_label\">{{ item.label }}</div>\n                  <div class=\"num_value\" :style=\"`color:${item.color}`\">{{ item.value }}</div>\n                </div>\n              </div>\n            </div>\n            <div class=\"committee_statistics_chart\">\n              <BarScrollChart id=\"committee-statistics\" :showCount=\"5\" :chart-data=\"committeeBarData\" />\n            </div>\n          </div>\n        </div>\n        <!-- 提案统计 -->\n        <div class=\"proposal_statistics\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\" @click=\"handleProposalClick\">提案统计</span>\n            <span class=\"header_text_right\">十二届二次会议</span>\n            <span class=\"header_text_center\">提交提案总数：<span>873</span>件</span>\n          </div>\n          <div class=\"proposal_statistics_content\">\n            <div class=\"proposal_statistics_num\">\n              <div v-for=\"(item, index) in proposalStatisticsNum\" :key=\"index\" class=\"num_box\">\n                <img :src=\"item.icon\" alt=\"\" class=\"num_icon\">\n                <div>\n                  <div class=\"num_label\">{{ item.label }}</div>\n                  <div class=\"num_value\" :style=\"`color:${item.color}`\">{{ item.value }}<span class=\"num_unit\">{{\n                    item.unit }}</span></div>\n                </div>\n              </div>\n            </div>\n            <div class=\"proposal_statistics_chart\">\n              <PieChart id=\"proposal-statistics\" :chart-data=\"proposalChartData\" :name=\"proposalChartName\"\n                legendIcon=\"circle\" :legendItemGap=\"12\" :radius=\"['60%', '85%']\" :center=\"['22%', '50%']\"\n                :lineRadius=\"['94%', '95%']\" :lineCenter=\"['22%', '50%']\" graphicLeft=\"12%\" graphicTop=\"23%\"\n                :graphicShapeR=\"40\" />\n            </div>\n          </div>\n        </div>\n        <!-- 工作动态 -->\n        <div class=\"work_dynamics\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\">工作动态</span>\n            <span class=\"header_text_right\">本年</span>\n          </div>\n          <div class=\"work_dynamics_content\">\n            <div class=\"dynamics-list\">\n              <div v-for=\"(item, index) in workDynamicsData\" :key=\"item.id\" class=\"dynamics-item\"\n                :class=\"{ 'with-bg-image': index % 2 === 0, 'with-bg-color': index % 2 === 1 }\">\n                <div class=\"dynamics-content\">\n                  <div class=\"dynamics-title\">{{ item.title }}</div>\n                  <div class=\"dynamics-date\">{{ item.date }}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div class=\"center-panel\">\n        <!-- 地图 -->\n        <div class=\"map_box\">\n          <MapComponent :data=\"mapData\" :areaId=\"areaId + ''\" :areaName=\"areaName\" @region-click=\"handleRegionClick\" />\n        </div>\n        <!-- 履职统计 -->\n        <div class=\"performance_statistics\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\" @click=\"handlePerformanceClick\">履职统计</span>\n            <span class=\"header_text_right\">十二届二次</span>\n          </div>\n          <div class=\"performance_statistics_content\">\n            <div class=\"table-container\">\n              <!-- 固定表头 -->\n              <div class=\"table-header\">\n                <div class=\"header-cell\">姓名</div>\n                <div class=\"header-cell\">会议活动</div>\n                <div class=\"header-cell\">政协提案</div>\n                <div class=\"header-cell\">社情民意</div>\n                <div class=\"header-cell\">议政建言</div>\n                <div class=\"header-cell\">读书心得</div>\n                <div class=\"header-cell\">委员培训</div>\n                <div class=\"header-cell\"></div> <!-- 滚动条占位 -->\n              </div>\n              <!-- 可滚动内容 -->\n              <div class=\"table-body\">\n                <div class=\"table-row\" v-for=\"(item, index) in performanceData\" :key=\"index\">\n                  <div class=\"table-cell name-col\">{{ item.name }}</div>\n                  <div class=\"table-cell meeting-col\">{{ item.meeting }}</div>\n                  <div class=\"table-cell proposal-col\">{{ item.proposal }}</div>\n                  <div class=\"table-cell opinion-col\">{{ item.opinion }}</div>\n                  <div class=\"table-cell suggestion-col\">{{ item.suggestion }}\n                  </div>\n                  <div class=\"table-cell reading-col\">{{ item.reading }}</div>\n                  <div class=\"table-cell training-col\">{{ item.training }}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div class=\"right-panel\">\n        <!-- 社情民意 -->\n        <div class=\"social\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\" @click=\"handlePublicOpinionClick\">社情民意</span>\n            <span class=\"header_text_right\">本年</span>\n          </div>\n          <div class=\"social_content\">\n            <div class=\"social-data-container\">\n              <div class=\"left-data-item\">\n                <div class=\"left-data-label\">委员报送</div>\n                <div class=\"left-data-value\">总数<span>{{ socialData.memberSubmit.count }}</span>篇</div>\n                <div class=\"left-data-detail\">采用<span>{{ socialData.memberSubmit.adopted }}</span> 篇</div>\n              </div>\n              <div class=\"center-chart\">\n                <div class=\"progress-content\">\n                  <div class=\"total-number\">{{ socialData.total }}</div>\n                  <div class=\"total-label\">总数</div>\n                </div>\n              </div>\n              <div class=\"right-data-item\">\n                <div class=\"right-data-label\">单位报送</div>\n                <div class=\"right-data-value\">总数<span>{{ socialData.unitSubmit.count }}</span>篇</div>\n                <div class=\"right-data-detail\">采用<span>{{ socialData.unitSubmit.adopted }}</span> 篇</div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <!-- 会议活动 -->\n        <div class=\"conference_activities\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\">会议活动</span>\n            <span class=\"header_text_right\">本年</span>\n          </div>\n          <div class=\"conference_activities_content\">\n            <div class=\"activities-grid\">\n              <div v-for=\"(item, index) in conferenceActivitiesData\" :key=\"index\" class=\"activity-item\"\n                :class=\"getItemClass(item.name)\">\n                <div class=\"activity-value\">{{ item.value }}</div>\n                <div class=\"activity-name\">{{ item.name }}</div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <!-- 网络议政 -->\n        <div class=\"discussions\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\" @click=\"handleNetWorkClick\">网络议政</span>\n            <span class=\"header_text_right\"></span>\n          </div>\n          <div class=\"discussions_content\">\n            <!-- 统计数据区域 -->\n            <div class=\"statistics-section\">\n              <div v-for=\"(item, index) in discussionsData.statistics\" :key=\"index\" class=\"stat-item\">\n                <div class=\"stat-dot\"></div>\n                <div class=\"stat-info\">\n                  <span class=\"stat-name\">{{ item.name }}</span>\n                  <span class=\"stat-value\">{{ item.value }}</span>\n                  <span class=\"stat-unit\">{{ item.unit }}</span>\n                </div>\n              </div>\n            </div>\n\n            <!-- 最热话题区域 -->\n            <div class=\"hot-topics-section\">\n              <div class=\"hot-topics-header\">\n                <img src=\"../../../assets/largeScreen/icon_hot.png\" alt=\"热门\" class=\"hot-icon\">\n                <span class=\"hot-title\">最热话题</span>\n              </div>\n              <div class=\"topics-list\">\n                <div v-for=\"(topic, index) in discussionsData.hotTopics\" :key=\"index\" class=\"topic-item\">\n                  <div class=\"topic-dot\"></div>\n                  <span class=\"topic-text\">{{ topic }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { useIndex } from '../screen.js'\nimport MapComponent from '../components/MapComponent.vue'\nimport PieChart from '../components/PieChart.vue'\nimport BarScrollChart from '../components/BarScrollChart.vue'\n\nexport default {\n  name: 'BigScreen',\n  components: {\n    MapComponent,\n    PieChart,\n    BarScrollChart\n  },\n  data () {\n    return {\n      currentTime: '',\n      // 委员统计\n      committeeStatisticsNum: [\n        { icon: require('../../../assets/largeScreen/icon_cppcc_member.png'), label: '政协委员（人）', value: '10095', color: '#ffffff' },\n        { icon: require('../../../assets/largeScreen/icon_committee_member.png'), label: '政协常委', value: '8742', color: '#FCD603' }\n      ],\n      // 委员统计柱状图数据\n      committeeBarData: [\n        { name: '中共', value: 32 },\n        { name: '民革', value: 15 },\n        { name: '民盟', value: 14 },\n        { name: '民建', value: 13 },\n        { name: '民进', value: 12 },\n        { name: '农工', value: 10 },\n        { name: '致公', value: 8 },\n        { name: '九三', value: 7 },\n        { name: '台盟', value: 6 },\n        { name: '无党派', value: 5 }\n      ],\n      // 提案统计\n      proposalStatisticsNum: [\n        { icon: require('../../../assets/largeScreen/icon_committee_proposal.png'), label: '委员提案', value: '456', unit: '件', color: '#0DBCDB' },\n        { icon: require('../../../assets/largeScreen/icon_circles_proposal.png'), label: '界别提案', value: '354', unit: '件', color: '#0058FF' },\n        { icon: require('../../../assets/largeScreen/icon_committee_proposal.png'), label: '组织提案', value: '211', unit: '件', color: '#1AEBDD' }\n      ],\n      // 提案统计图表数据\n      proposalChartData: [\n        { name: '政府制约', value: 22.52 },\n        { name: '县区市政', value: 18.33 },\n        { name: '司法法治', value: 15.73 },\n        { name: '区市政府', value: 11.34 },\n        { name: '科技工商', value: 9.56 },\n        { name: '教育文化', value: 8.09 },\n        { name: '派出机构', value: 4.21 },\n        { name: '社会事业', value: 3.71 },\n        { name: '企事业', value: 3.65 },\n        { name: '农村卫生', value: 3.21 },\n        { name: '其他机构', value: 1.86 },\n        { name: '各群体他', value: 1.02 }\n      ],\n      proposalChartName: '提案统计',\n      // 工作动态数据\n      workDynamicsData: [\n        {\n          id: 1,\n          title: '市政协社会和法制工作办公室围绕\"居家适老化改造\"开展专题调研',\n          date: '2025-06-03'\n        },\n        {\n          id: 2,\n          title: '\"与民同行 共创共赢\"新格局下民营企业转型发展座谈会召开',\n          date: '2025-05-30'\n        },\n        {\n          id: 3,\n          title: '\"惠民生·基层行\"义诊活动温暖人心',\n          date: '2025-05-30'\n        },\n        {\n          id: 4,\n          title: '市科技局面复市政协科技界别提案',\n          date: '2025-05-30'\n        },\n        {\n          id: 5,\n          title: '市政协召开\"推进数字化转型\"专题协商会',\n          date: '2025-05-28'\n        },\n        {\n          id: 6,\n          title: '政协委员深入基层开展\"三服务\"活动',\n          date: '2025-05-25'\n        }\n      ],\n      // 履职统计数据\n      performanceData: [\n        { name: '马平安', meeting: 515, proposal: 15, opinion: 0, suggestion: 0, reading: 0, training: 0 },\n        { name: '马波', meeting: 400, proposal: 0, opinion: 0, suggestion: 12, reading: 0, training: 15 },\n        { name: '王玉民', meeting: 490, proposal: 1, opinion: 2, suggestion: 0, reading: 4, training: 25 },\n        { name: '王俊宝', meeting: 500, proposal: 0, opinion: 4, suggestion: 1, reading: 5, training: 60 },\n        { name: '李明', meeting: 320, proposal: 8, opinion: 1, suggestion: 3, reading: 2, training: 18 },\n        { name: '张华', meeting: 280, proposal: 5, opinion: 0, suggestion: 2, reading: 1, training: 12 },\n        { name: '刘强', meeting: 450, proposal: 3, opinion: 6, suggestion: 0, reading: 3, training: 35 },\n        { name: '陈静', meeting: 380, proposal: 2, opinion: 3, suggestion: 4, reading: 6, training: 28 }\n      ],\n      // 社情民意数据\n      socialData: {\n        memberSubmit: {\n          count: 345,\n          adopted: 21\n        },\n        unitSubmit: {\n          count: 547,\n          adopted: 79\n        },\n        total: 1057\n      },\n      // 会议活动数据\n      conferenceActivitiesData: [\n        { name: '会议次数', value: 201 },\n        { name: '活动次数', value: 310 },\n        { name: '会议人数', value: 2412 },\n        { name: '活动人数', value: 4015 }\n      ],\n      // 网络议政数据\n      discussionsData: {\n        statistics: [\n          { name: '发布议题', value: 72, unit: '个' },\n          { name: '累计参与人次', value: 39301, unit: '次' },\n          { name: '累计征求意见', value: 12306, unit: '条' }\n        ],\n        hotTopics: [\n          '推进黄河国家文化公园建设',\n          '持续推进黄河流域生态保护修复，助力\"先行区\"建设',\n          '全面加强新时代中小学劳动教育'\n        ]\n      },\n      mapData: [\n        { name: '市南区', value: '1200', areaId: '370202', adcode: 370202 },\n        { name: '市北区', value: '1300', areaId: '370203', adcode: 370203 },\n        { name: '黄岛区', value: '850', areaId: '370211', adcode: 370211 },\n        { name: '崂山区', value: '700', areaId: '370212', adcode: 370212 },\n        { name: '李沧区', value: '1000', areaId: '370213', adcode: 370213 },\n        { name: '城阳区', value: '1100', areaId: '370214', adcode: 370214 },\n        { name: '即墨区', value: '950', areaId: '370215', adcode: 370215 },\n        { name: '胶州市', value: '800', areaId: '370281', adcode: 370281 },\n        { name: '平度市', value: '1400', areaId: '370283', adcode: 370283 },\n        { name: '莱西市', value: '600', areaId: '370285', adcode: 370285 }\n      ],\n      areaId: JSON.parse(sessionStorage.getItem('areaId' + this.$logo())),\n      areaName: '青岛市'\n    }\n  },\n  computed: {\n  },\n  mounted () {\n    this.initScreen()\n    this.updateTime()\n    this.timeInterval = setInterval(this.updateTime, 1000)\n  },\n  beforeDestroy () {\n    if (this.timeInterval) {\n      clearInterval(this.timeInterval)\n    }\n  },\n  methods: {\n    initScreen () {\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\n      calcRate()\n      windowDraw()\n    },\n    updateTime () {\n      const now = new Date()\n      this.currentTime = now.toLocaleString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit',\n        second: '2-digit'\n      })\n    },\n    getItemClass (name) {\n      if (name.includes('会议')) {\n        return 'meeting-item'\n      } else if (name.includes('活动')) {\n        return 'activity-item-bg'\n      }\n      return ''\n    },\n    handleRegionClick (region) {\n      console.log('选中地区:', region)\n      // 这里可以添加地区点击后的业务逻辑\n      // 比如显示该地区的详细数据等\n    },\n    // 打开委员统计\n    handleCommitteeClick () {\n      this.$router.push({ path: '/committeeStatisticsBox', query: { route: '/committeeStatisticsBox' } })\n    },\n    // 打开提案统计\n    handleProposalClick () {\n      this.$router.push({ path: '/proposalStatisticsBox', query: { route: '/proposalStatisticsBox' } })\n    },\n    // 打开履职统计\n    handlePerformanceClick () {\n      this.$router.push({ path: '/performanceStatisticsBox', query: { route: '/performanceStatisticsBox' } })\n    },\n    // 打开网络议政\n    handleNetWorkClick () {\n      this.$router.push({ path: '/networkDiscussBox', query: { route: '/networkDiscussBox' } })\n    },\n    // 打开社情民意\n    handlePublicOpinionClick () {\n      this.$router.push({ path: '/publicOpinionBox', query: { route: '/publicOpinionBox' } })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.big-screen {\n  width: 1920px;\n  height: 1080px;\n  position: relative;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  transform-origin: left top;\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\n  background-size: cover;\n  background-position: center;\n\n  .screen-header {\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\n    background-size: 100% 100%;\n    background-position: center;\n    height: 65px;\n    display: flex;\n    align-items: center;\n    padding: 0 40px;\n\n    .header-left {\n      display: flex;\n      gap: 20px;\n      font-size: 14px;\n      color: #8cc8ff;\n      flex: 1;\n    }\n\n    .header-center {\n      width: 60%;\n      text-align: center;\n    }\n\n    .header-right {\n      flex: 1;\n    }\n  }\n\n  .screen-content {\n    height: calc(100% - 65px);\n    display: flex;\n    padding: 35px 20px 0 20px;\n    gap: 30px;\n\n    .header_box {\n      position: absolute;\n      top: 15px;\n      left: 24px;\n      right: 0;\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n\n      .header_text_left {\n        font-weight: bold;\n        font-size: 20px;\n        color: #FFFFFF;\n        cursor: pointer;\n      }\n\n      .header_text_right {\n        font-size: 15px;\n        color: #FFD600;\n      }\n\n      .header_text_center {\n        font-size: 15px;\n        color: #FFFFFF;\n        display: flex;\n        align-items: center;\n\n        span {\n          font-weight: 500;\n          font-size: 24px;\n          color: #02FBFB;\n          margin: 0 10px 0 6px;\n        }\n      }\n    }\n\n    .left-panel,\n    .right-panel {\n      width: 470px;\n      display: flex;\n      flex-direction: column;\n      gap: 20px 30px;\n    }\n\n    .left-panel {\n      .committee_statistics {\n        background: url('../../../assets/largeScreen/committee_statistics_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 320px;\n        width: 100%;\n\n        .committee_statistics_content {\n          height: 100%;\n          margin-top: 72px;\n          margin-left: 20px;\n          margin-right: 20px;\n\n          .committee_statistics_num {\n            display: flex;\n            align-items: center;\n            justify-content: space-around;\n\n            .num_box {\n              display: flex;\n              align-items: center;\n\n              .num_icon {\n                width: 64px;\n                height: 64px;\n                margin-right: 14px;\n              }\n\n              .num_label {\n                font-size: 15px;\n                color: #B4C0CC;\n                margin-bottom: 14px;\n              }\n\n              .num_value {\n                font-weight: bold;\n                font-size: 26px;\n                color: #FFFFFF;\n              }\n            }\n          }\n\n          .committee_statistics_chart {\n            width: 100%;\n            height: 180px;\n          }\n        }\n      }\n\n      .proposal_statistics {\n        background: url('../../../assets/largeScreen/proposal_statistics_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 310px;\n        width: 100%;\n\n        .proposal_statistics_content {\n          height: 100%;\n          margin-top: 72px;\n          margin-left: 20px;\n          margin-right: 20px;\n\n          .proposal_statistics_num {\n            display: flex;\n            align-items: center;\n            justify-content: space-between;\n\n            .num_box {\n              display: flex;\n              align-items: center;\n\n              .num_icon {\n                width: 54px;\n                height: 54px;\n                margin-right: 10px;\n              }\n\n              .num_label {\n                font-size: 14px;\n                color: #FFFFFF;\n                margin-bottom: 5px;\n              }\n\n              .num_value {\n                font-size: 20px;\n                color: #0DBCDB;\n                font-weight: 500;\n\n                .num_unit {\n                  font-size: 14px;\n                  color: #FFFFFF;\n                  font-weight: normal;\n                  margin-left: 4px;\n                }\n              }\n            }\n          }\n\n          .proposal_statistics_chart {\n            height: 150px;\n            margin-top: 20px;\n          }\n        }\n      }\n\n      .work_dynamics {\n        background: url('../../../assets/largeScreen/work_dynamics_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 270px;\n        width: 100%;\n\n        .work_dynamics_content {\n          height: 100%;\n          margin-top: 65px;\n          margin-left: 14px;\n          margin-right: 14px;\n\n          .dynamics-list {\n            height: calc(100% - 70px);\n            overflow-y: auto;\n\n            &::-webkit-scrollbar {\n              width: 4px;\n            }\n\n            &::-webkit-scrollbar-track {\n              background: rgba(0, 30, 60, 0.3);\n              border-radius: 2px;\n            }\n\n            &::-webkit-scrollbar-thumb {\n              background: rgba(0, 212, 255, 0.4);\n              border-radius: 2px;\n\n              &:hover {\n                background: rgba(0, 212, 255, 0.6);\n              }\n            }\n\n            .dynamics-item {\n              margin-bottom: 12px;\n              overflow: hidden;\n              position: relative;\n\n              &:last-child {\n                margin-bottom: 0;\n              }\n\n              // 奇数项 - 背景图片样式\n              &.with-bg-image {\n                background: url('../../../assets/largeScreen/table_bg.png') no-repeat;\n                background-size: 100% 100%;\n                background-position: center;\n              }\n\n              // 偶数项 - 背景颜色样式\n              &.with-bg-color {\n                background: rgba(6, 79, 219, 0.05);\n              }\n\n              .dynamics-content {\n                padding: 12px 15px;\n                position: relative;\n                z-index: 2;\n                display: flex;\n                justify-content: space-between;\n                align-items: center;\n\n                .dynamics-title {\n                  flex: 1;\n                  color: #fff;\n                  font-size: 16px;\n                  margin-right: 16px;\n                  // 文本溢出处理\n                  display: -webkit-box;\n                  -webkit-line-clamp: 1;\n                  -webkit-box-orient: vertical;\n                  overflow: hidden;\n                  text-overflow: ellipsis;\n                }\n\n                .dynamics-date {\n                  flex-shrink: 0;\n                  font-size: 16px;\n                  color: #FFFFFF;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    .right-panel {\n      .social {\n        background: url('../../../assets/largeScreen/social_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 290px;\n        width: 100%;\n\n        .social_content {\n          height: 190px;\n          margin-top: 75px;\n          margin-left: 12px;\n          margin-right: 12px;\n          background: url('../../../assets/largeScreen/social_content_bg.png') no-repeat;\n          background-size: 100% 100%;\n          background-position: center;\n\n          .social-data-container {\n            display: flex;\n            align-items: center;\n            justify-content: space-between;\n            width: 100%;\n            height: 100%;\n\n            .left-data-item {\n              display: flex;\n              flex-direction: column;\n              align-items: center;\n              text-align: center;\n              flex: 1;\n              margin-right: 20px;\n\n              .left-data-label {\n                font-size: 14px;\n                color: #19ECFF;\n                margin-bottom: 20px;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n              }\n\n              .left-data-value {\n                font-size: 14px;\n                color: #FFFFFF;\n                margin-bottom: 15px;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n\n                span {\n                  font-weight: 400;\n                  font-size: 20px;\n                  color: #FFD600;\n                  margin: 0 5px;\n                }\n              }\n\n              .left-data-detail {\n                font-size: 14px;\n                color: #FFFFFF;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n\n                span {\n                  font-weight: 400;\n                  font-size: 20px;\n                  color: #19ECFF;\n                  margin: 0 5px;\n                }\n              }\n            }\n\n            .center-chart {\n              flex: 1;\n              display: flex;\n              justify-content: center;\n              align-items: center;\n\n              .progress-content {\n                position: absolute;\n                display: flex;\n                flex-direction: column;\n                align-items: center;\n                justify-content: center;\n\n                .total-number {\n                  font-weight: 500;\n                  font-size: 24px;\n                  color: #FFD600;\n                  margin-bottom: 8px;\n                }\n\n                .total-label {\n                  font-size: 14px;\n                  color: #ffffff;\n                }\n              }\n            }\n\n            .right-data-item {\n              display: flex;\n              flex-direction: column;\n              align-items: center;\n              text-align: center;\n              flex: 1;\n              margin-left: 20px;\n\n              .right-data-label {\n                font-size: 14px;\n                color: #19ECFF;\n                margin-bottom: 20px;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n              }\n\n              .right-data-value {\n                font-size: 14px;\n                color: #FFFFFF;\n                margin-bottom: 15px;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n\n                span {\n                  font-weight: 400;\n                  font-size: 20px;\n                  color: #FFD600;\n                  margin: 0 5px;\n                }\n              }\n\n              .right-data-detail {\n                font-size: 14px;\n                color: #FFFFFF;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n\n                span {\n                  font-weight: 400;\n                  font-size: 20px;\n                  color: #19ECFF;\n                  margin: 0 5px;\n                }\n              }\n            }\n          }\n        }\n      }\n\n      .conference_activities {\n        background: url('../../../assets/largeScreen/conference_activities_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 290px;\n        width: 100%;\n\n        .conference_activities_content {\n          margin-top: 70px;\n          margin-left: 20px;\n          margin-right: 20px;\n\n          .activities-grid {\n            display: grid;\n            grid-template-columns: 1fr 1fr;\n            grid-template-rows: 1fr 1fr;\n            gap: 15px;\n            height: 100%;\n\n            .activity-item {\n              display: flex;\n              flex-direction: column;\n              align-items: center;\n              justify-content: center;\n              background-size: 100% 100%;\n              background-repeat: no-repeat;\n              background-position: center;\n              height: 92px;\n\n              .activity-value {\n                font-weight: 500;\n                font-size: 32px;\n                color: #FFFFFF;\n                line-height: 24px;\n                margin-bottom: 12px;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n              }\n\n              .activity-name {\n                font-size: 16px;\n                color: #FFFFFF;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n              }\n\n              &.meeting-item {\n                background-image: url('../../../assets/largeScreen/icon_meeting_item_bg.png');\n              }\n\n              &.activity-item-bg {\n                background-image: url('../../../assets/largeScreen/icon_activity_item_bg.png');\n              }\n            }\n          }\n        }\n      }\n\n      .discussions {\n        background: url('../../../assets/largeScreen/discussions_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 320px;\n        width: 100%;\n\n        .discussions_content {\n          margin-top: 75px;\n          margin-left: 20px;\n          margin-right: 20px;\n          height: calc(100% - 90px);\n          display: flex;\n          flex-direction: column;\n\n          .statistics-section {\n            display: flex;\n            flex-wrap: wrap;\n            justify-content: space-between;\n            gap: 20px;\n            margin-bottom: 25px;\n\n            .stat-item {\n              display: flex;\n              align-items: center;\n              gap: 12px;\n\n              .stat-dot {\n                width: 10px;\n                height: 10px;\n                border-radius: 50%;\n                background: linear-gradient(180deg, #00EEFF 0%, #E1FDFF 100%);\n                flex-shrink: 0;\n                margin-top: 5px;\n              }\n\n              .stat-info {\n                display: flex;\n                align-items: center;\n                gap: 8px;\n\n                .stat-name {\n                  font-size: 15px;\n                  color: #FFFFFF;\n                  font-family: DIN-BoldItalic, DIN-BoldItalic;\n                }\n\n                .stat-value {\n                  font-weight: 500;\n                  font-size: 20px;\n                  color: #FFD600;\n                  font-family: DIN-BoldItalic, DIN-BoldItalic;\n                }\n\n                .stat-unit {\n                  font-size: 15px;\n                  color: #FFFFFF;\n                  font-family: DIN-BoldItalic, DIN-BoldItalic;\n                }\n              }\n            }\n          }\n\n          .hot-topics-section {\n            flex: 1;\n            background: rgba(31, 198, 255, 0.16);\n            padding: 12px 16px;\n\n            .hot-topics-header {\n              display: flex;\n              align-items: center;\n              gap: 8px;\n              margin-bottom: 15px;\n\n              .hot-icon {\n                width: 20px;\n                height: 20px;\n              }\n\n              .hot-title {\n                font-size: 16px;\n                color: #02FBFB;\n                font-weight: 500;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n              }\n            }\n\n            .topics-list {\n              display: flex;\n              flex-direction: column;\n              gap: 12px;\n\n              .topic-item {\n                display: flex;\n                align-items: flex-start;\n                gap: 10px;\n\n                .topic-dot {\n                  width: 6px;\n                  height: 6px;\n                  border-radius: 50%;\n                  background: rgba(217, 217, 217, 0.5);\n                  margin-top: 8px;\n                  flex-shrink: 0;\n                }\n\n                .topic-text {\n                  font-size: 14px;\n                  color: #FFFFFF;\n                  line-height: 22px;\n                  font-family: DIN-BoldItalic, DIN-BoldItalic;\n                  text-overflow: ellipsis;\n                  overflow: hidden;\n                  white-space: nowrap;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    .center-panel {\n      flex: 1;\n      gap: 20px;\n      display: flex;\n      flex-direction: column;\n\n      .map_box {\n        background: url('../../../assets/largeScreen/map_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        height: 650px;\n        width: 100%;\n        position: relative;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n\n      .performance_statistics {\n        background: url('../../../assets/largeScreen/performance_statistics_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 270px;\n        width: 100%;\n\n        .performance_statistics_content {\n          height: 100%;\n          margin-top: 65px;\n          margin-left: 16px;\n          margin-right: 16px;\n\n          .table-container {\n            height: calc(100% - 75px);\n            display: flex;\n            flex-direction: column;\n            border: 1px solid rgba(0, 212, 255, 0.2);\n            overflow: hidden;\n            /* 使用CSS Grid确保列对齐 */\n            --name-col-width: 120px;\n            --scrollbar-width: 6px;\n\n            .table-header {\n              display: grid;\n              grid-template-columns: var(--name-col-width) repeat(6, 1fr) var(--scrollbar-width);\n              border-bottom: 1px solid rgba(0, 212, 255, 0.4);\n              position: sticky;\n              top: 0;\n              z-index: 10;\n\n              .header-cell {\n                padding: 12px 8px;\n                text-align: center;\n                color: #E6F7FF;\n                font-size: 15px;\n                border-right: 1px solid rgba(0, 212, 255, 0.3);\n                display: flex;\n                align-items: center;\n                justify-content: center;\n\n                &:last-child {\n                  border-right: none;\n                  background: transparent;\n                  border: none;\n                }\n\n                // &.name-col {\n                //   background: rgba(0, 100, 180, 0.9);\n                //   font-weight: 600;\n                // }\n              }\n            }\n\n            .table-body {\n              flex: 1;\n              overflow-y: auto;\n\n              &::-webkit-scrollbar {\n                width: 6px;\n              }\n\n              &::-webkit-scrollbar-track {\n                background: rgba(0, 30, 60, 0.3);\n                border-radius: 3px;\n              }\n\n              &::-webkit-scrollbar-thumb {\n                background: rgba(0, 212, 255, 0.4);\n                border-radius: 3px;\n\n                &:hover {\n                  background: rgba(0, 212, 255, 0.6);\n                }\n              }\n\n              .table-row {\n                display: grid;\n                grid-template-columns: var(--name-col-width) repeat(6, 1fr);\n                border-bottom: 1px solid rgba(0, 212, 255, 0.4);\n                transition: all 0.3s ease;\n\n                &:hover {\n                  background: rgba(0, 212, 255, 0.1);\n                }\n\n                .table-cell {\n                  padding: 12px 8px;\n                  text-align: center;\n                  color: #FFFFFF;\n                  font-size: 14px;\n                  border-right: 1px solid rgba(0, 212, 255, 0.4);\n                  transition: all 0.3s ease;\n                  display: flex;\n                  align-items: center;\n                  justify-content: center;\n\n                  &:last-child {\n                    border-right: none;\n                  }\n\n                  &.name-col {\n                    background: rgba(0, 60, 120, 0.4);\n                    color: #FFF;\n                    font-weight: 500;\n                  }\n\n                  &.meeting-col {\n                    background: rgba(10, 63, 111, 0.4);\n                    color: #59F7CA;\n                    font-weight: 500;\n                    font-size: 16px;\n                  }\n\n                  &.proposal-col {\n                    background: rgba(10, 63, 111, 0.4);\n                    font-weight: 500;\n                    font-size: 16px;\n                    color: #00FFF7;\n                  }\n\n                  &.opinion-col {\n                    background: rgba(10, 63, 111, 0.4);\n                    font-weight: 500;\n                    font-size: 16px;\n                    color: #FF386B;\n                  }\n\n                  &.suggestion-col {\n                    background: rgba(10, 63, 111, 0.4);\n                    font-weight: 500;\n                    font-size: 16px;\n                    color: #81C4E4;\n                  }\n\n                  &.reading-col {\n                    background: rgba(10, 63, 111, 0.4);\n                    font-weight: 500;\n                    font-size: 16px;\n                    color: #387BFD;\n                  }\n\n                  &.training-col {\n                    background: rgba(10, 63, 111, 0.4);\n                    font-weight: 500;\n                    font-size: 16px;\n                    color: #FF911F;\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}