{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\proposalStatistics\\proposalStatisticsBox.vue?vue&type=style&index=0&id=778a9eeb&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\proposalStatistics\\proposalStatisticsBox.vue", "mtime": 1756455988588}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["proposalStatisticsBox.vue"], "names": [], "mappings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file": "proposalStatisticsBox.vue", "sourceRoot": "src/views/smartBrainLargeScreen/proposalStatistics", "sourcesContent": ["<template>\r\n  <div class=\"big-screen\" ref=\"bigScreen\">\r\n    <div class=\"screen-header\">\r\n      <div class=\"header-left\">\r\n        <span class=\"date-time\">{{ currentTime }}</span>\r\n        <span class=\"weather\">晴 24℃ 东南风</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <div class=\"header-buttons\">\r\n          <div class=\"header-btn current-module-btn\">\r\n            <span>提案统计</span>\r\n          </div>\r\n          <div class=\"header-btn home-btn\" @click=\"goHome\">\r\n            <span>返回首页</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"screen-content\">\r\n      <div class=\"left-panel\">\r\n        <!-- 类别分布 -->\r\n        <div class=\"category_distribution\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">类别分布</span>\r\n          </div>\r\n          <div class=\"category_distribution_content\">\r\n            <PieChart id=\"category_distribution\" :chart-data=\"categoryChartData\" :name=\"categoryChartName\"\r\n              legendIcon=\"circle\" :legendItemGap=\"30\" :radius=\"['30%', '45%']\" :center=\"['50%', '25%']\"\r\n              :lineRadius=\"['50%', '51%']\" :lineCenter=\"['50%', '25%']\" graphicLeft=\"36.5%\" graphicTop=\"13%\"\r\n              :graphicShapeR=\"60\" />\r\n          </div>\r\n        </div>\r\n        <!-- 热词分析 -->\r\n        <div class=\"hot_word_analysis\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">热词分析</span>\r\n          </div>\r\n          <div class=\"hot_word_analysis_content\">\r\n            <WordCloud chart-id=\"hotWordChart\" :words=\"hotWordsData\" @word-click=\"onWordClick\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"center-panel\">\r\n        <!-- 提案整体情况 -->\r\n        <div class=\"proposal_overall_situation\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">提案整体情况</span>\r\n          </div>\r\n          <div class=\"proposal_overall_situation_content\">\r\n            <!-- 左侧数据卡片 -->\r\n            <div class=\"left-section\">\r\n              <div class=\"data-card total-proposals\">\r\n                <div class=\"card-number\">{{ proposalOverallData.totalProposals }}</div>\r\n                <div class=\"card-label\">提案总件数</div>\r\n              </div>\r\n              <div class=\"data-card approved-proposals\">\r\n                <div class=\"card-number\">{{ proposalOverallData.approvedProposals }}</div>\r\n                <div class=\"card-label\">立案总件数</div>\r\n              </div>\r\n              <div class=\"data-card replied-proposals\">\r\n                <div class=\"card-number\">{{ proposalOverallData.repliedProposals }}</div>\r\n                <div class=\"card-label\">答复总件数</div>\r\n              </div>\r\n            </div>\r\n            <!-- 右侧图表区域 -->\r\n            <div class=\"right-section\">\r\n              <div class=\"top-charts\">\r\n                <div class=\"chart-item approval-rate\">\r\n                  <CircularProgress id=\"approval-rate-chart\" :percentage=\"proposalOverallData.approvalRate\" label=\"立案率\"\r\n                    color=\"#00d4ff\" />\r\n                </div>\r\n                <div class=\"chart-item reply-rate\">\r\n                  <CircularProgress id=\"reply-rate-chart\" :percentage=\"proposalOverallData.replyRate\" label=\"答复率\"\r\n                    color=\"#ffd700\" />\r\n                </div>\r\n              </div>\r\n              <div class=\"bottom-section\">\r\n                <div class=\"reply-pie-chart\">\r\n                  <PieChart id=\"reply-type-pie\" :chart-data=\"replyTypeChartData\" name=\"答复类型\" :legendItemGap=\"40\"\r\n                    :legendItemWidth=\"12\" :legendItemHeight=\"6\" :radius=\"['40%', '70%']\" :center=\"['50%', '50%']\"\r\n                    :lineRadius=\"['1%', '1%']\" :lineCenter=\"['50%', '50%']\" graphicLeft=\"40%\" graphicTop=\"35%\"\r\n                    :graphicShapeR=\"0\" />\r\n                </div>\r\n                <div class=\"reply-progress\">\r\n                  <ProgressBar :progress-data=\"replyTypeProgressData\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 各专委会提案数 -->\r\n        <div class=\"committee_proposal_number\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">各专委会提案数</span>\r\n          </div>\r\n          <div class=\"committee_proposal_content\">\r\n            <BarChart id=\"committee_proposal\" :chart-data=\"committeeProposalData\" :legendShow=\"true\"\r\n              legendName=\"提交件数\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"right-panel\">\r\n        <!-- 提交情况 -->\r\n        <div class=\"submission_status\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">提交情况</span>\r\n          </div>\r\n          <div class=\"submission_status_content\">\r\n            <div class=\"submission_item\" v-for=\"item in submissionStatusData\" :key=\"item.name\">\r\n              <div class=\"submission_icon\">\r\n                <img :src=\"require(`../../../assets/largeScreen/${item.icon}`)\" alt=\"\">\r\n                <div class=\"submission_name\">{{ item.name }}</div>\r\n              </div>\r\n              <div class=\"submission_value\">{{ item.value }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 办理单位统计前十 -->\r\n        <div class=\"hand_unit\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">办理单位统计（前十）</span>\r\n          </div>\r\n          <div class=\"hand_unit_content\">\r\n            <RankingBarChart id=\"handling_unit_chart\" :chart-data=\"handlingUnitData\" :show-values=\"true\"\r\n              :max-value=\"100\" />\r\n          </div>\r\n        </div>\r\n        <!-- 重点提案 -->\r\n        <div class=\"key_proposals\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">重点提案</span>\r\n            <span class=\"header_text_right\"></span>\r\n          </div>\r\n          <div class=\"key_proposals_list\">\r\n            <div v-for=\"(item, index) in keyProposalsData\" :key=\"item.id\" class=\"key_proposals_item\"\r\n              :class=\"{ 'with-bg-image': index % 2 === 0, 'with-bg-color': index % 2 === 1 }\">\r\n              <div class=\"key_proposals_content\">\r\n                <div class=\"key_proposals_title\">{{ item.title }}</div>\r\n                <div class=\"key_proposals_name\">{{ item.name }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { useIndex } from '../screen.js'\r\nimport PieChart from '../components/PieChart.vue'\r\nimport WordCloud from '../components/WordCloud.vue'\r\nimport CircularProgress from '../components/CircularProgress.vue'\r\nimport ProgressBar from '../components/ProgressBar.vue'\r\nimport BarChart from '../components/BarChart.vue'\r\nimport RankingBarChart from '../components/RankingBarChart.vue'\r\n\r\nexport default {\r\n  name: 'BigScreen',\r\n  components: {\r\n    PieChart,\r\n    WordCloud,\r\n    CircularProgress,\r\n    ProgressBar,\r\n    BarChart,\r\n    RankingBarChart\r\n  },\r\n  data () {\r\n    return {\r\n      currentTime: '',\r\n      categoryChartName: '类别分析',\r\n      categoryChartData: [\r\n        { name: '政府制约', value: 22.52 },\r\n        { name: '县区市政', value: 18.33 },\r\n        { name: '司法法治', value: 15.73 },\r\n        { name: '区市政府', value: 11.34 },\r\n        { name: '科技工商', value: 9.56 },\r\n        { name: '教育文化', value: 8.09 },\r\n        { name: '派出机构', value: 4.21 },\r\n        { name: '社会事业', value: 3.71 },\r\n        { name: '企事业', value: 3.65 },\r\n        { name: '农村卫生', value: 3.21 },\r\n        { name: '其他机构', value: 1.86 },\r\n        { name: '各群体他', value: 1.02 }\r\n      ],\r\n      hotWordsData: [\r\n        { name: '经济建设', value: 10 },\r\n        { name: '人才培养', value: 9 },\r\n        { name: 'AI技术', value: 6 },\r\n        { name: '改革创新', value: 7 },\r\n        { name: '教育', value: 5 },\r\n        { name: '车辆交通', value: 6 },\r\n        { name: '旅游', value: 5 },\r\n        { name: '公共安全', value: 7 },\r\n        { name: '智能化', value: 6 },\r\n        { name: '电梯故障', value: 4 },\r\n        { name: '社会保障', value: 6 },\r\n        { name: '环境保护', value: 5 },\r\n        { name: '医疗卫生', value: 7 },\r\n        { name: '文化建设', value: 4 },\r\n        { name: '科技创新', value: 8 }\r\n      ],\r\n      // 提案整体情况数据\r\n      proposalOverallData: {\r\n        totalProposals: 1500,\r\n        approvedProposals: 600,\r\n        repliedProposals: 600,\r\n        approvalRate: 69,\r\n        replyRate: 69\r\n      },\r\n      // 答复类型统一数据源\r\n      replyTypeData: [\r\n        {\r\n          name: '面复',\r\n          value: 360,\r\n          color: '#00d4ff'\r\n        },\r\n        {\r\n          name: '函复',\r\n          value: 240,\r\n          color: '#ffd700'\r\n        }\r\n      ],\r\n      // 各专委会提案数数据\r\n      committeeProposalData: [\r\n        { name: '提案委', value: 43 },\r\n        { name: '经济委', value: 67 },\r\n        { name: '农业农村委', value: 84 },\r\n        { name: '人口资源环境委', value: 52 },\r\n        { name: '教科卫体委', value: 36 },\r\n        { name: '社会和法制委', value: 66 },\r\n        { name: '民族宗教委', value: 26 },\r\n        { name: '港澳台侨外事委', value: 60 },\r\n        { name: '文化文史和学习委', value: 46 }\r\n      ],\r\n      // 提交情况数据\r\n      submissionStatusData: [\r\n        {\r\n          name: '委员提案',\r\n          value: 270,\r\n          icon: 'icon_committee_proposal_situation.png'\r\n        },\r\n        {\r\n          name: '界别提案',\r\n          value: 290,\r\n          icon: 'icon_sector_proposal_situation.png'\r\n        },\r\n        {\r\n          name: '组织提案',\r\n          value: 170,\r\n          icon: 'icon_organizational_proposal_situation.png'\r\n        }\r\n      ],\r\n      // 办理单位统计前十数据\r\n      handlingUnitData: [\r\n        { name: '政府', value: 89 },\r\n        { name: '政协办公厅', value: 75 },\r\n        { name: '发展改革委', value: 70 },\r\n        { name: '教育体育局', value: 63 },\r\n        { name: '民政局', value: 60 },\r\n        { name: '财政局', value: 50 },\r\n        { name: '人社局', value: 46 },\r\n        { name: '自然资源局', value: 46 },\r\n        { name: '生态环境局', value: 44 },\r\n        { name: '文化旅游局', value: 41 }\r\n      ],\r\n      // 重点提案数据\r\n      keyProposalsData: [\r\n        {\r\n          id: 1,\r\n          title: '市政协社会和法制工作办公室围绕\"居家适老化改造\"开展专题调研',\r\n          name: '赵国胜'\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '\"与民同行 共创共赢\"新格局下民营企业转型发展座谈会召开',\r\n          name: '李颖之'\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '\"惠民生·基层行\"义诊活动温暖人心',\r\n          name: '王红妮'\r\n        },\r\n        {\r\n          id: 4,\r\n          title: '市科技局面复市政协科技界别提案',\r\n          name: '张万强'\r\n        },\r\n        {\r\n          id: 5,\r\n          title: '市政协召开\"推进数字化转型\"专题协商会',\r\n          name: '张万强'\r\n        },\r\n        {\r\n          id: 6,\r\n          title: '政协委员深入基层开展\"三服务\"活动',\r\n          name: '张万强'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    // 为饼图组件提供数据格式\r\n    replyTypeChartData () {\r\n      return this.replyTypeData.map(item => ({\r\n        name: item.name,\r\n        value: item.value,\r\n        color: item.color\r\n      }))\r\n    },\r\n\r\n    // 为进度条组件提供数据格式\r\n    replyTypeProgressData () {\r\n      const total = this.replyTypeData.reduce((sum, item) => sum + item.value, 0)\r\n      return this.replyTypeData.map(item => ({\r\n        label: item.name,\r\n        value: item.value,\r\n        percent: Math.round((item.value / total) * 100),\r\n        color: item.color\r\n      }))\r\n    }\r\n  },\r\n  mounted () {\r\n    this.initScreen()\r\n    this.updateTime()\r\n    this.timeInterval = setInterval(this.updateTime, 1000)\r\n  },\r\n  beforeDestroy () {\r\n    if (this.timeInterval) {\r\n      clearInterval(this.timeInterval)\r\n    }\r\n  },\r\n  methods: {\r\n    initScreen () {\r\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\r\n      calcRate()\r\n      windowDraw()\r\n    },\r\n    updateTime () {\r\n      const now = new Date()\r\n      this.currentTime = now.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      })\r\n    },\r\n    // 返回首页\r\n    goHome () {\r\n      this.$router.push({ path: '/homeBox' })\r\n    },\r\n    // 词云点击事件\r\n    onWordClick (word) {\r\n      console.log('词汇点击:', word)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.big-screen {\r\n  width: 1920px;\r\n  height: 1080px;\r\n  position: relative;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transform-origin: left top;\r\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\r\n  background-size: cover;\r\n  background-position: center;\r\n\r\n  .screen-header {\r\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\r\n    background-size: 100% 100%;\r\n    background-position: center;\r\n    height: 65px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 40px;\r\n\r\n    .header-left {\r\n      display: flex;\r\n      gap: 20px;\r\n      font-size: 14px;\r\n      color: #8cc8ff;\r\n      flex: 1;\r\n    }\r\n\r\n    .header-center {\r\n      width: 60%;\r\n      text-align: center;\r\n    }\r\n\r\n    .header-right {\r\n      flex: 1;\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      align-items: center;\r\n\r\n      .header-buttons {\r\n        display: flex;\r\n        gap: 15px;\r\n\r\n        .header-btn {\r\n          height: 42px;\r\n          line-height: 42px;\r\n          padding: 0 16px;\r\n          cursor: pointer;\r\n          transition: all 0.3s ease;\r\n          position: relative;\r\n          overflow: hidden;\r\n\r\n          &::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: -100%;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n            transition: left 0.5s;\r\n          }\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);\r\n            border-color: rgba(0, 181, 254, 1);\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);\r\n\r\n            &::before {\r\n              left: 100%;\r\n            }\r\n          }\r\n\r\n          &.current-module-btn {\r\n            background: url('../../../assets/largeScreen/icon_committee.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: bold;\r\n            font-size: 16px;\r\n            color: #FFFFFF;\r\n          }\r\n\r\n          &.home-btn {\r\n            background: url('../../../assets/largeScreen/icon_back_home.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: 400;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n          }\r\n\r\n          &.area-select-btn {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 0.2) 0%, rgba(0, 120, 220, 0.2) 100%);\r\n            border: 1px solid rgba(0, 181, 254, 0.5);\r\n            border-radius: 6px;\r\n            font-weight: 500;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            min-width: 120px;\r\n\r\n            .dropdown-icon {\r\n              margin-left: 8px;\r\n              font-size: 12px;\r\n              transition: transform 0.3s ease;\r\n              color: #1FC6FF;\r\n\r\n              &.active {\r\n                transform: rotate(180deg);\r\n              }\r\n            }\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(0, 181, 254, 0.3) 0%, rgba(0, 120, 220, 0.3) 100%);\r\n              border-color: rgba(0, 181, 254, 0.8);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .screen-content {\r\n    height: calc(100% - 65px);\r\n    display: flex;\r\n    padding: 35px 20px 0 20px;\r\n    gap: 30px;\r\n\r\n    .header_box {\r\n      position: absolute;\r\n      top: 15px;\r\n      left: 24px;\r\n      right: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n\r\n      .header_text_left {\r\n        font-weight: bold;\r\n        font-size: 20px;\r\n        color: #FFFFFF;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .header_text_right {\r\n        font-size: 15px;\r\n        color: #FFD600;\r\n      }\r\n\r\n      .header_text_center {\r\n        font-size: 15px;\r\n        color: #FFFFFF;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          font-weight: 500;\r\n          font-size: 24px;\r\n          color: #02FBFB;\r\n          margin: 0 10px 0 6px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .left-panel,\r\n    .right-panel {\r\n      width: 470px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 30px 30px;\r\n    }\r\n\r\n    .left-panel {\r\n      .category_distribution {\r\n        background: url('../../../assets/largeScreen/category_distribution_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 585px;\r\n        width: 100%;\r\n\r\n        .category_distribution_content {\r\n          height: calc(100% - 70px);\r\n          margin-top: 70px;\r\n        }\r\n      }\r\n\r\n      .hot_word_analysis {\r\n        background: url('../../../assets/largeScreen/hot_word_analysis_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 320px;\r\n        width: 100%;\r\n\r\n        .hot_word_analysis_content {\r\n          height: calc(100% - 92px);\r\n          margin-top: 72px;\r\n          margin-bottom: 20px;\r\n          position: relative;\r\n        }\r\n      }\r\n    }\r\n\r\n    .right-panel {\r\n      .submission_status {\r\n        background: url('../../../assets/largeScreen/submission_status_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 245px;\r\n        width: 100%;\r\n\r\n        .submission_status_content {\r\n          margin-top: 60px;\r\n          margin-left: 20px;\r\n          margin-right: 20px;\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          height: calc(100% - 80px);\r\n\r\n          .submission_item {\r\n            flex: 1;\r\n            display: flex;\r\n            flex-direction: column;\r\n            align-items: center;\r\n            justify-content: center;\r\n            position: relative;\r\n            height: 120px;\r\n            margin: 0 10px;\r\n\r\n            .submission_icon {\r\n              width: 100px;\r\n              height: 100px;\r\n              margin-bottom: 8px;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              position: relative;\r\n              z-index: 2;\r\n\r\n              img {\r\n                width: 100%;\r\n                height: 100%;\r\n                object-fit: contain;\r\n              }\r\n\r\n              // 文字覆盖在图片上\r\n              .submission_name {\r\n                position: absolute;\r\n                top: 50%;\r\n                left: 50%;\r\n                transform: translate(-50%, -50%);\r\n                font-size: 14px;\r\n                color: #FFFFFF;\r\n                white-space: nowrap;\r\n              }\r\n            }\r\n\r\n            .submission_value {\r\n              font-weight: 500;\r\n              font-size: 20px;\r\n              color: #02FBFB;\r\n              font-family: 'Arial', sans-serif;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .hand_unit {\r\n        background: url('../../../assets/largeScreen/hand_unit_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 315px;\r\n        width: 100%;\r\n\r\n        .hand_unit_content {\r\n          margin-top: 60px;\r\n          margin-left: 20px;\r\n          margin-right: 20px;\r\n          height: calc(100% - 80px);\r\n        }\r\n      }\r\n\r\n      .key_proposals {\r\n        background: url('../../../assets/largeScreen/key_proposals_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 315px;\r\n        width: 100%;\r\n\r\n        .key_proposals_list {\r\n          margin-top: 60px;\r\n          margin-left: 14px;\r\n          margin-right: 14px;\r\n          height: calc(100% - 70px);\r\n          overflow-y: auto;\r\n\r\n          &::-webkit-scrollbar {\r\n            width: 4px;\r\n          }\r\n\r\n          &::-webkit-scrollbar-track {\r\n            background: rgba(0, 30, 60, 0.3);\r\n            border-radius: 2px;\r\n          }\r\n\r\n          &::-webkit-scrollbar-thumb {\r\n            background: rgba(0, 212, 255, 0.4);\r\n            border-radius: 2px;\r\n\r\n            &:hover {\r\n              background: rgba(0, 212, 255, 0.6);\r\n            }\r\n          }\r\n\r\n          .key_proposals_item {\r\n            margin-bottom: 12px;\r\n            overflow: hidden;\r\n            position: relative;\r\n\r\n            &:last-child {\r\n              margin-bottom: 0;\r\n            }\r\n\r\n            // 奇数项 - 背景图片样式\r\n            &.with-bg-image {\r\n              background: url('../../../assets/largeScreen/table_bg.png') no-repeat;\r\n              background-size: 100% 100%;\r\n              background-position: center;\r\n            }\r\n\r\n            // 偶数项 - 背景颜色样式\r\n            &.with-bg-color {\r\n              background: rgba(6, 79, 219, 0.05);\r\n            }\r\n\r\n            .key_proposals_content {\r\n              padding: 12px 15px;\r\n              position: relative;\r\n              z-index: 2;\r\n              display: flex;\r\n              justify-content: space-between;\r\n              align-items: center;\r\n\r\n              .key_proposals_title {\r\n                flex: 1;\r\n                color: #fff;\r\n                font-size: 16px;\r\n                margin-right: 16px;\r\n                // 文本溢出处理\r\n                display: -webkit-box;\r\n                -webkit-line-clamp: 1;\r\n                -webkit-box-orient: vertical;\r\n                overflow: hidden;\r\n                text-overflow: ellipsis;\r\n              }\r\n\r\n              .key_proposals_name {\r\n                flex-shrink: 0;\r\n                font-size: 16px;\r\n                color: #FFFFFF;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .center-panel {\r\n      flex: 1;\r\n      gap: 30px;\r\n      display: flex;\r\n      flex-direction: column;\r\n\r\n      .proposal_overall_situation {\r\n        background: url('../../../assets/largeScreen/overall_situation_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        height: 505px;\r\n        width: 100%;\r\n        position: relative;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        .proposal_overall_situation_content {\r\n          width: 100%;\r\n          height: calc(100% - 70px);\r\n          margin-top: 70px;\r\n          margin-left: 40px;\r\n          margin-right: 40px;\r\n          display: flex;\r\n          gap: 50px;\r\n\r\n          .left-section {\r\n            display: flex;\r\n            flex-direction: column;\r\n            justify-content: space-around;\r\n            margin: 10px 0;\r\n\r\n            .data-card {\r\n              width: 133px;\r\n              height: 98px;\r\n              display: flex;\r\n              flex-direction: column;\r\n              justify-content: center;\r\n              align-items: center;\r\n\r\n              &.total-proposals {\r\n                background: url('../../../assets/largeScreen/icon_proposal_total.png') no-repeat;\r\n                background-size: 100% 100%;\r\n                background-position: center;\r\n              }\r\n\r\n              &.approved-proposals {\r\n                background: url('../../../assets/largeScreen/icon_proposal_total.png') no-repeat;\r\n                background-size: 100% 100%;\r\n                background-position: center;\r\n              }\r\n\r\n              &.replied-proposals {\r\n                background: url('../../../assets/largeScreen/icon_reply_total.png') no-repeat;\r\n                background-size: 100% 100%;\r\n                background-position: center;\r\n              }\r\n\r\n              .card-number {\r\n                font-weight: 500;\r\n                font-size: 32px;\r\n                margin-bottom: 8px;\r\n              }\r\n\r\n              &.total-proposals .card-number {\r\n                color: #FFFFFF;\r\n              }\r\n\r\n              &.approved-proposals .card-number {\r\n                color: #1FC6FF;\r\n              }\r\n\r\n              &.replied-proposals .card-number {\r\n                color: #F5E74F;\r\n              }\r\n\r\n              .card-label {\r\n                font-size: 16px;\r\n                color: #FFFFFF;\r\n                opacity: 0.9;\r\n              }\r\n            }\r\n          }\r\n\r\n          .right-section {\r\n            flex: 1;\r\n            display: flex;\r\n            flex-direction: column;\r\n            // gap: 100px;\r\n\r\n            .top-charts {\r\n              display: flex;\r\n              justify-content: space-evenly;\r\n\r\n              .chart-item {\r\n                width: 199px;\r\n                height: 199px;\r\n                position: relative;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n\r\n                &.approval-rate {\r\n                  background: url('../../../assets/largeScreen/icon_case_filing.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                  z-index: 1; // 背景图最低优先级\r\n                }\r\n\r\n                &.reply-rate {\r\n                  background: url('../../../assets/largeScreen/icon_reply_rate.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                  z-index: 1; // 背景图最低优先级\r\n                }\r\n\r\n                // 确保圆形进度图在背景图中央，但在文字下方\r\n                .circular-progress {\r\n                  position: absolute;\r\n                  top: 50%;\r\n                  left: 50%;\r\n                  transform: translate(-50%, -50%);\r\n                  width: 140px;\r\n                  height: 140px;\r\n                  z-index: 50; // 图表中等优先级\r\n                }\r\n              }\r\n            }\r\n\r\n            .bottom-section {\r\n              display: flex;\r\n              justify-content: space-evenly;\r\n              width: 100%;\r\n              height: 230px;\r\n\r\n              .reply-pie-chart {\r\n                width: 55%;\r\n                height: 100%;\r\n              }\r\n\r\n              .reply-progress {\r\n                width: 45%;\r\n                height: 100%;\r\n                display: flex;\r\n                align-items: center;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .committee_proposal_number {\r\n        background: url('../../../assets/largeScreen/committee_proposal_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 400px;\r\n        width: 100%;\r\n\r\n        .committee_proposal_content {\r\n          height: 100%;\r\n          margin-top: 65px;\r\n          margin-left: 16px;\r\n          margin-right: 16px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}