{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart.vue", "mtime": 1756456123625}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAKA;AAEA;EACAA,gBADA;EAEAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAJ;MACAG,YADA;MAEAC;IAFA,CALA;IASAC;MACAF,YADA;MAEAG,WAFA;MAGAF;IAHA,CATA;IAcAG;MACAJ,YADA;MAEAG,WAFA;MAGAF;IAHA,CAdA;IAmBAI;MACAL,YADA;MAEAG,UAFA;MAGAF;IAHA,CAnBA;IAwBAK;MACAN,YADA;MAEAG,UAFA;MAGAF;IAHA,CAxBA;IA6BAM;MACAP,WADA;MAEAC,eAFA;MAGAE;IAHA,CA7BA;IAkCAK;MACAR,WADA;MAEAC,eAFA;MAGAE;IAHA,CAlCA;IAuCAM;MACAT,WADA;MAEAC,eAFA;MAGAE;IAHA,CAvCA;IA4CAO;MACAV,WADA;MAEAC,eAFA;MAGAE;IAHA,CA5CA;IAiDAQ;MACAX,YADA;MAEAG,cAFA;MAGAF;IAHA,CAjDA;IAsDAW;MACAZ,YADA;MAEAG,cAFA;MAGAF;IAHA,CAtDA;IA2DAY;MACAb,YADA;MAEAG,WAFA;MAGAF;IAHA,CA3DA;IAgEAa;MACAd,WADA;MAEAC,cAFA;MAGAE;IAHA;EAhEA,CAFA;;EAwEAY;IACA;MACAC,WADA;MAEAC,wBAFA;MAEA;MACAC,yBAHA;MAGA;MACA;MACAC,SACA,SADA,EACA,SADA,EACA,SADA,EACA,SADA,EAEA,SAFA,EAEA,SAFA,EAEA,SAFA,EAEA,SAFA,EAGA,SAHA,EAGA,SAHA,EAGA,SAHA,EAGA,SAHA,EAIA,SAJA,EAIA,SAJA,EAIA,SAJA,EAIA,SAJA,EAKA,SALA,EAKA,SALA,EAKA,SALA,EAKA,SALA;IALA;EAaA,CAtFA;;EAuFAC;IACA;EACA,CAzFA;;EA0FAC;IACA;IACA;MACAC;IACA;;IACA;MACA;IACA;EACA,CAlGA;;EAmGAC;IACAC;MACA;MACA;QACA;MACA,CAJA,CAKA;;;MACA;IACA,CARA;;IASAC;MACA;MACA;MACA;MACA;QACAC;UACAC,eADA;UAEAC,qCAFA;UAGAC,qCAHA;UAIAC,sBAJA;UAKAC,cALA;UAMAC;YACAC;UADA;QANA,CADA;QAWAC;UACAC,uEADA;UAEAC,yIAFA;UAGAC,2DAHA;UAIAC,iGAJA;UAKAC,uDALA;UAMAC,+BANA;UAOAC,iCAPA;UAQAC,qBARA;UASAC,2BATA;UAUA;UACAX;YACAC,aADA;YAEAW,YAFA;YAGAC;UAHA,CAXA;UAgBAjB;YACA;;YACA;cACA;YACA,CAFA,MAEA;cACA;YACA;UACA;QAvBA,CAXA;QAoCAkB,SACA;UACAjD,eADA;UAEAG,WAFA;UAGAO,mBAHA;UAIAC,mBAJA;UAKAuC,wBALA;UAMAC;YACAC,WADA;YAEAC;UAFA,CANA;UAUAC;YACAC,UADA;YAEAC,kBAFA;YAGAT,YAHA;YAIAX,aAJA;YAKAL;UALA,CAVA;UAiBA0B;YACAF;UADA,CAjBA;UAoBAG;YACAxB,cADA;YAEAD,sBAFA,CAEA;;UAFA,CApBA;UAwBAf;YACAyC,iBADA;YAEA3D,eAFA;YAGA0D;cAAAtB;YAAA;UAHA;QAxBA,CADA,EA+BA;UACAjC,WADA;UAEAO,uBAFA;UAGAC,uBAHA;UAIAO,OACA;YACAyC,UADA;YAEAD;cACAtB;YADA;UAFA,CADA,CAJA;UAYAkB;YACAC;UADA;QAZA,CA/BA,CApCA;QAoFAK,UACA;UACAzD,cADA;UAEAqC,sBAFA;UAGAC,oBAHA;UAIAoB;YACAC,KADA;YAEAC,KAFA;YAGAC;UAHA,CAJA;UASAC;YACAC,YADA;YAEAC,sDAFA;YAGAC;cACAjE,cADA;cAEAkE,IAFA;cAGAC,IAHA;cAIAC,KAJA;cAKAC,KALA;cAMAC,aACA;gBAAAC;gBAAAtC;cAAA,CADA,EAEA;gBAAAsC;gBAAAtC;cAAA,CAFA;YANA;UAHA,CATA;UAwBAuC,KAxBA;UAyBAC,YAzBA;UA0BApB;QA1BA,CADA;MApFA;MAmHA,6BAvHA,CAwHA;;MACAqB;QACA;UACA;QACA;MACA,CAJA,EAzHA,CA8HA;;MACA,0BA/HA,CAgIA;;MACA;QACA;MACA,CAFA;MAGA;QACA;MACA,CAFA;IAGA,CAhJA;;IAkJA;IACAC;MACA;MAEA;QACA;QACA;UACA;YACA3E,gBADA;YAEA4E,cAFA;YAGAC;UAHA,GADA,CAMA;;UACA;YACA7E;UADA;QAGA,CAZA,CAcA;;;QACA;QACA;UACAA,iBADA;UAEA4E,cAFA;UAGAC;QAHA,GAhBA,CAsBA;;QACA;UACA7E,eADA;UAEA4E,cAFA;UAGAC;QAHA;MAKA,CA5BA,EA4BA,IA5BA,EAHA,CA+BA;IACA,CAnLA;;IAqLA;IACAC;MACA;QACAxD;QACA;MACA,CAJA,CAKA;;;MACA;QACA;UACAtB,gBADA;UAEA4E,cAFA;UAGAC;QAHA;QAKA;MACA;IACA;;EApMA;AAnGA", "names": ["name", "props", "id", "type", "required", "legendIcon", "default", "legendItemGap", "legend<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendItemHeight", "radius", "center", "lineRadius", "lineCenter", "graphicLeft", "graphicTop", "graphicShapeR", "chartData", "data", "chart", "autoHighlightTimer", "currentHighlightIndex", "colors", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "getColor", "initChart", "tooltip", "trigger", "formatter", "backgroundColor", "borderColor", "borderWidth", "textStyle", "color", "legend", "orient", "right", "left", "top", "bottom", "itemWidth", "itemHeight", "icon", "itemGap", "fontSize", "fontFamily", "series", "avoidLabelOverlap", "emphasis", "scale", "scaleSize", "label", "show", "position", "labelLine", "itemStyle", "value", "graphic", "shape", "cx", "cy", "r", "style", "fill", "lineWidth", "stroke", "x", "y", "x2", "y2", "colorStops", "offset", "z", "silent", "window", "startAutoHighlight", "seriesIndex", "dataIndex", "stopAutoHighlight"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["PieChart.vue"], "sourcesContent": ["<template>\n  <div :id=\"id\" class=\"pie-chart\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: '<PERSON><PERSON><PERSON>',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    name: {\n      type: String,\n      required: true\n    },\n    legendIcon: {\n      type: String,\n      default: '',\n      required: false\n    },\n    legendItemGap: {\n      type: Number,\n      default: 25,\n      required: false\n    },\n    legendItemWidth: {\n      type: Number,\n      default: 5,\n      required: false\n    },\n    legendItemHeight: {\n      type: Number,\n      default: 5,\n      required: false\n    },\n    radius: {\n      type: Array,\n      required: false,\n      default: () => ['55%', '80%']\n    },\n    center: {\n      type: Array,\n      required: false,\n      default: () => ['30%', '50%']\n    },\n    lineRadius: {\n      type: Array,\n      required: false,\n      default: () => ['88%', '89%']\n    },\n    lineCenter: {\n      type: Array,\n      required: false,\n      default: () => ['30%', '50%']\n    },\n    graphicLeft: {\n      type: String,\n      default: '17%',\n      required: false\n    },\n    graphicTop: {\n      type: String,\n      default: '26%',\n      required: false\n    },\n    graphicShapeR: {\n      type: Number,\n      default: 50,\n      required: false\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    }\n  },\n  data () {\n    return {\n      chart: null,\n      autoHighlightTimer: null, // 自动高亮定时器\n      currentHighlightIndex: -1, // 当前高亮的数据项索引\n      // 预定义的颜色数组，按顺序分配给数据项\n      colors: [\n        '#4FC3F7', '#FFF67C', '#66BB6A', '#FFA726',\n        '#FF7043', '#AB47BC', '#5C6BC0', '#42A5F5',\n        '#FFCA28', '#4CAF50', '#EF5350', '#8D6E63',\n        '#9C27B0', '#3F51B5', '#2196F3', '#00BCD4',\n        '#FF9800', '#795548', '#607D8B', '#E91E63'\n      ]\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    // 清除自动高亮定时器\n    if (this.autoHighlightTimer) {\n      clearInterval(this.autoHighlightTimer)\n    }\n    if (this.chart) {\n      this.chart.dispose()\n    }\n  },\n  methods: {\n    getColor (index, item) {\n      // 如果数据项中有颜色信息，优先使用数据中的颜色\n      if (item && item.color) {\n        return item.color\n      }\n      // 否则使用预定义的颜色数组\n      return this.colors[index % this.colors.length]\n    },\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      const option = {\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)',\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          borderColor: '#00d4ff',\n          borderWidth: 1,\n          textStyle: {\n            color: '#fff'\n          }\n        },\n        legend: {\n          orient: this.id === 'category_distribution' ? 'horizontal' : 'vertical',\n          right: this.id === 'reply-type-pie' ? '0%' : this.id === 'category_distribution' ? null : this.id === 'proposal-statistics' ? '0%' : '5%',\n          left: this.id === 'category_distribution' ? 'center' : null,\n          top: this.id === 'reply-type-pie' ? '38%' : this.id === 'category_distribution' ? null : 'center',\n          bottom: this.id === 'category_distribution' ? 20 : null,\n          itemWidth: this.legendItemWidth,\n          itemHeight: this.legendItemHeight,\n          icon: this.legendIcon,\n          itemGap: this.legendItemGap,\n          // itemGap: this.id === 'reply-type-pie' ? 40 : this.id === 'category_distribution' ? 30 : this.id === 'proposal-statistics' ? 12 : 25,\n          textStyle: {\n            color: '#fff',\n            fontSize: 12,\n            fontFamily: 'Microsoft YaHei'\n          },\n          formatter: (name) => {\n            const item = this.chartData.find(d => d.name === name)\n            if (this.id === 'reply-type-pie') {\n              return `${name}`\n            } else {\n              return `${name}  ${item ? item.value : ''}%`\n            }\n          }\n        },\n        series: [\n          {\n            name: this.name,\n            type: 'pie',\n            radius: this.radius,\n            center: this.center,\n            avoidLabelOverlap: false,\n            emphasis: {\n              scale: true,\n              scaleSize: 10\n            },\n            label: {\n              show: true,\n              position: 'center',\n              fontSize: 16,\n              color: '#fff',\n              formatter: this.name\n            },\n            labelLine: {\n              show: false\n            },\n            itemStyle: {\n              borderWidth: 3,\n              borderColor: '#07345F' // 用大屏背景色\n            },\n            data: this.chartData.map((item, index) => ({\n              value: item.value,\n              name: item.name,\n              itemStyle: { color: this.getColor(index, item) }\n            }))\n          },\n          {\n            type: 'pie',\n            radius: this.lineRadius,\n            center: this.lineCenter,\n            data: [\n              {\n                value: 100,\n                itemStyle: {\n                  color: '#2f689a'\n                }\n              }\n            ],\n            label: {\n              show: false\n            }\n          }\n        ],\n        graphic: [\n          {\n            type: 'circle',\n            left: this.graphicLeft,\n            top: this.graphicTop,\n            shape: {\n              cx: 0,\n              cy: 0,\n              r: this.graphicShapeR\n            },\n            style: {\n              fill: 'none',\n              lineWidth: this.id === 'category_distribution' ? 4 : 3,\n              stroke: {\n                type: 'linear',\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [\n                  { offset: 0, color: '#23E1FF' },\n                  { offset: 1, color: 'rgba(35,225,255,0)' }\n                ]\n              }\n            },\n            z: 10,\n            silent: true,\n            position: [0, 0]\n          }\n        ]\n      }\n      this.chart.setOption(option)\n      // 监听窗口大小变化\n      window.addEventListener('resize', () => {\n        if (this.chart) {\n          this.chart.resize()\n        }\n      })\n      // 启动自动高亮效果\n      this.startAutoHighlight()\n      // 添加鼠标事件监听\n      this.chart.on('mouseover', () => {\n        this.stopAutoHighlight()\n      })\n      this.chart.on('mouseout', () => {\n        this.startAutoHighlight()\n      })\n    },\n\n    // 开始自动高亮效果\n    startAutoHighlight () {\n      if (this.chartData.length === 0) return\n\n      this.autoHighlightTimer = setInterval(() => {\n        // 取消当前高亮和tooltip\n        if (this.currentHighlightIndex >= 0) {\n          this.chart.dispatchAction({\n            type: 'downplay',\n            seriesIndex: 0,\n            dataIndex: this.currentHighlightIndex\n          })\n          // 隐藏tooltip\n          this.chart.dispatchAction({\n            type: 'hideTip'\n          })\n        }\n\n        // 高亮下一个数据项\n        this.currentHighlightIndex = (this.currentHighlightIndex + 1) % this.chartData.length\n        this.chart.dispatchAction({\n          type: 'highlight',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n\n        // 显示tooltip\n        this.chart.dispatchAction({\n          type: 'showTip',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n      }, 2000) // 每2秒切换一次\n    },\n\n    // 停止自动高亮效果\n    stopAutoHighlight () {\n      if (this.autoHighlightTimer) {\n        clearInterval(this.autoHighlightTimer)\n        this.autoHighlightTimer = null\n      }\n      // 取消所有高亮\n      if (this.chart && this.currentHighlightIndex >= 0) {\n        this.chart.dispatchAction({\n          type: 'downplay',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n        this.currentHighlightIndex = -1\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.pie-chart {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n</style>\n"]}]}