{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart.vue", "mtime": 1756455001794}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCAqIGFzIGVjaGFydHMgZnJvbSAnZWNoYXJ0cycKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnUGllQ2hhcnQnLAogIHByb3BzOiB7CiAgICBpZDogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9LAogICAgbmFtZTogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9LAogICAgbGVnZW5kSWNvbjogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcnLAogICAgICByZXF1aXJlZDogZmFsc2UKICAgIH0sCiAgICBsZWdlbmRJdGVtR2FwOiB7CiAgICAgIHR5cGU6IE51bWJlciwKICAgICAgZGVmYXVsdDogMjUsCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9LAogICAgbGVnZW5kSXRlbVdpZHRoOiB7CiAgICAgIHR5cGU6IE51bWJlciwKICAgICAgZGVmYXVsdDogNSwKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0sCiAgICBsZWdlbmRJdGVtSGVpZ2h0OiB7CiAgICAgIHR5cGU6IE51bWJlciwKICAgICAgZGVmYXVsdDogNSwKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0sCiAgICBjaGFydERhdGE6IHsKICAgICAgdHlwZTogQXJyYXksCiAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICBkZWZhdWx0OiAoKSA9PiBbXQogICAgfQogIH0sCiAgZGF0YSAoKSB7CiAgICByZXR1cm4gewogICAgICBjaGFydDogbnVsbCwKICAgICAgYXV0b0hpZ2hsaWdodFRpbWVyOiBudWxsLCAvLyDoh6rliqjpq5jkuq7lrprml7blmagKICAgICAgY3VycmVudEhpZ2hsaWdodEluZGV4OiAtMSwgLy8g5b2T5YmN6auY5Lqu55qE5pWw5o2u6aG557Si5byVCiAgICAgIC8vIOmihOWumuS5ieeahOminOiJsuaVsOe7hO+8jOa<PERSON>iemhuuW6j+WIhumFjee7meaVsOaNrumhuQogICAgICBjb2xvcnM6IFsKICAgICAgICAnIzRGQzNGNycsICcjRkZGNjdDJywgJyM2NkJCNkEnLCAnI0ZGQTcyNicsCiAgICAgICAgJyNGRjcwNDMnLCAnI0FCNDdCQycsICcjNUM2QkMwJywgJyM0MkE1RjUnLAogICAgICAgICcjRkZDQTI4JywgJyM0Q0FGNTAnLCAnI0VGNTM1MCcsICcjOEQ2RTYzJywKICAgICAgICAnIzlDMjdCMCcsICcjM0Y1MUI1JywgJyMyMTk2RjMnLCAnIzAwQkNENCcsCiAgICAgICAgJyNGRjk4MDAnLCAnIzc5NTU0OCcsICcjNjA3RDhCJywgJyNFOTFFNjMnCiAgICAgIF0KICAgIH0KICB9LAogIG1vdW50ZWQgKCkgewogICAgdGhpcy5pbml0Q2hhcnQoKQogIH0sCiAgYmVmb3JlRGVzdHJveSAoKSB7CiAgICAvLyDmuIXpmaToh6rliqjpq5jkuq7lrprml7blmagKICAgIGlmICh0aGlzLmF1dG9IaWdobGlnaHRUaW1lcikgewogICAgICBjbGVhckludGVydmFsKHRoaXMuYXV0b0hpZ2hsaWdodFRpbWVyKQogICAgfQogICAgaWYgKHRoaXMuY2hhcnQpIHsKICAgICAgdGhpcy5jaGFydC5kaXNwb3NlKCkKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIGdldENvbG9yIChpbmRleCwgaXRlbSkgewogICAgICAvLyDlpoLmnpzmlbDmja7pobnkuK3mnInpopzoibLkv6Hmga/vvIzkvJjlhYjkvb/nlKjmlbDmja7kuK3nmoTpopzoibIKICAgICAgaWYgKGl0ZW0gJiYgaXRlbS5jb2xvcikgewogICAgICAgIHJldHVybiBpdGVtLmNvbG9yCiAgICAgIH0KICAgICAgLy8g5ZCm5YiZ5L2/55So6aKE5a6a5LmJ55qE6aKc6Imy5pWw57uECiAgICAgIHJldHVybiB0aGlzLmNvbG9yc1tpbmRleCAlIHRoaXMuY29sb3JzLmxlbmd0aF0KICAgIH0sCiAgICBpbml0Q2hhcnQgKCkgewogICAgICBjb25zdCBjaGFydENvbnRhaW5lciA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKHRoaXMuaWQpCiAgICAgIGlmICghY2hhcnRDb250YWluZXIpIHJldHVybgogICAgICB0aGlzLmNoYXJ0ID0gZWNoYXJ0cy5pbml0KGNoYXJ0Q29udGFpbmVyKQogICAgICBjb25zdCBvcHRpb24gPSB7CiAgICAgICAgdG9vbHRpcDogewogICAgICAgICAgdHJpZ2dlcjogJ2l0ZW0nLAogICAgICAgICAgZm9ybWF0dGVyOiAne2F9IDxici8+e2J9OiB7Y30gKHtkfSUpJywKICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYmEoMCwgMCwgMCwgMC44KScsCiAgICAgICAgICBib3JkZXJDb2xvcjogJyMwMGQ0ZmYnLAogICAgICAgICAgYm9yZGVyV2lkdGg6IDEsCiAgICAgICAgICB0ZXh0U3R5bGU6IHsKICAgICAgICAgICAgY29sb3I6ICcjZmZmJwogICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgbGVnZW5kOiB7CiAgICAgICAgICBvcmllbnQ6IHRoaXMuaWQgPT09ICdjYXRlZ29yeV9kaXN0cmlidXRpb24nID8gJ2hvcml6b250YWwnIDogJ3ZlcnRpY2FsJywKICAgICAgICAgIHJpZ2h0OiB0aGlzLmlkID09PSAncmVwbHktdHlwZS1waWUnID8gJzAlJyA6IHRoaXMuaWQgPT09ICdjYXRlZ29yeV9kaXN0cmlidXRpb24nID8gbnVsbCA6IHRoaXMuaWQgPT09ICdwcm9wb3NhbC1zdGF0aXN0aWNzJyA/ICcwJScgOiAnNSUnLAogICAgICAgICAgbGVmdDogdGhpcy5pZCA9PT0gJ2NhdGVnb3J5X2Rpc3RyaWJ1dGlvbicgPyAnY2VudGVyJyA6IG51bGwsCiAgICAgICAgICB0b3A6IHRoaXMuaWQgPT09ICdyZXBseS10eXBlLXBpZScgPyAnMzglJyA6IHRoaXMuaWQgPT09ICdjYXRlZ29yeV9kaXN0cmlidXRpb24nID8gbnVsbCA6ICdjZW50ZXInLAogICAgICAgICAgYm90dG9tOiB0aGlzLmlkID09PSAnY2F0ZWdvcnlfZGlzdHJpYnV0aW9uJyA/IDIwIDogbnVsbCwKICAgICAgICAgIGl0ZW1XaWR0aDogdGhpcy5sZWdlbmRJdGVtV2lkdGgsCiAgICAgICAgICBpdGVtSGVpZ2h0OiB0aGlzLmxlZ2VuZEl0ZW1IZWlnaHQsCiAgICAgICAgICBpY29uOiB0aGlzLmxlZ2VuZEljb24sCiAgICAgICAgICBpdGVtR2FwOiB0aGlzLmxlZ2VuZEl0ZW1HYXAsCiAgICAgICAgICAvLyBpdGVtR2FwOiB0aGlzLmlkID09PSAncmVwbHktdHlwZS1waWUnID8gNDAgOiB0aGlzLmlkID09PSAnY2F0ZWdvcnlfZGlzdHJpYnV0aW9uJyA/IDMwIDogdGhpcy5pZCA9PT0gJ3Byb3Bvc2FsLXN0YXRpc3RpY3MnID8gMTIgOiAyNSwKICAgICAgICAgIHRleHRTdHlsZTogewogICAgICAgICAgICBjb2xvcjogJyNmZmYnLAogICAgICAgICAgICBmb250U2l6ZTogMTIsCiAgICAgICAgICAgIGZvbnRGYW1pbHk6ICdNaWNyb3NvZnQgWWFIZWknCiAgICAgICAgICB9LAogICAgICAgICAgZm9ybWF0dGVyOiAobmFtZSkgPT4gewogICAgICAgICAgICBjb25zdCBpdGVtID0gdGhpcy5jaGFydERhdGEuZmluZChkID0+IGQubmFtZSA9PT0gbmFtZSkKICAgICAgICAgICAgaWYgKHRoaXMuaWQgPT09ICdyZXBseS10eXBlLXBpZScpIHsKICAgICAgICAgICAgICByZXR1cm4gYCR7bmFtZX1gCiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgcmV0dXJuIGAke25hbWV9ICAke2l0ZW0gPyBpdGVtLnZhbHVlIDogJyd9JWAKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgc2VyaWVzOiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIG5hbWU6IHRoaXMubmFtZSwKICAgICAgICAgICAgdHlwZTogJ3BpZScsCiAgICAgICAgICAgIHJhZGl1czogdGhpcy5pZCA9PT0gJ2NhdGVnb3J5X2Rpc3RyaWJ1dGlvbicgPyBbJzMwJScsICc0NSUnXSA6IHRoaXMuaWQgPT09ICdwcm9wb3NhbC1zdGF0aXN0aWNzJyA/IFsnNjAlJywgJzg1JSddIDogdGhpcy5pZCA9PT0gJ3JlcGx5LXR5cGUtcGllJyA/IFsnNDAlJywgJzcwJSddIDogWyc1NSUnLCAnODAlJ10sCiAgICAgICAgICAgIGNlbnRlcjogdGhpcy5pZCA9PT0gJ2NhdGVnb3J5X2Rpc3RyaWJ1dGlvbicgPyBbJzUwJScsICcyNSUnXSA6IHRoaXMuaWQgPT09ICdwcm9wb3NhbC1zdGF0aXN0aWNzJyA/IFsnMjIlJywgJzUwJSddIDogdGhpcy5pZCA9PT0gJ3JlcGx5LXR5cGUtcGllJyA/IFsnNTAlJywgJzUwJSddIDogWyczMCUnLCAnNTAlJ10sCiAgICAgICAgICAgIGF2b2lkTGFiZWxPdmVybGFwOiBmYWxzZSwKICAgICAgICAgICAgZW1waGFzaXM6IHsKICAgICAgICAgICAgICBzY2FsZTogdHJ1ZSwKICAgICAgICAgICAgICBzY2FsZVNpemU6IDEwCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGxhYmVsOiB7CiAgICAgICAgICAgICAgc2hvdzogdHJ1ZSwKICAgICAgICAgICAgICBwb3NpdGlvbjogJ2NlbnRlcicsCiAgICAgICAgICAgICAgZm9udFNpemU6IDE2LAogICAgICAgICAgICAgIGNvbG9yOiAnI2ZmZicsCiAgICAgICAgICAgICAgZm9ybWF0dGVyOiB0aGlzLm5hbWUKICAgICAgICAgICAgfSwKICAgICAgICAgICAgbGFiZWxMaW5lOiB7CiAgICAgICAgICAgICAgc2hvdzogZmFsc2UKICAgICAgICAgICAgfSwKICAgICAgICAgICAgaXRlbVN0eWxlOiB7CiAgICAgICAgICAgICAgYm9yZGVyV2lkdGg6IDMsCiAgICAgICAgICAgICAgYm9yZGVyQ29sb3I6ICcjMDczNDVGJyAvLyDnlKjlpKflsY/og4zmma/oibIKICAgICAgICAgICAgfSwKICAgICAgICAgICAgZGF0YTogdGhpcy5jaGFydERhdGEubWFwKChpdGVtLCBpbmRleCkgPT4gKHsKICAgICAgICAgICAgICB2YWx1ZTogaXRlbS52YWx1ZSwKICAgICAgICAgICAgICBuYW1lOiBpdGVtLm5hbWUsCiAgICAgICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiB0aGlzLmdldENvbG9yKGluZGV4LCBpdGVtKSB9CiAgICAgICAgICAgIH0pKQogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgdHlwZTogJ3BpZScsCiAgICAgICAgICAgIHJhZGl1czogdGhpcy5pZCA9PT0gJ2NhdGVnb3J5X2Rpc3RyaWJ1dGlvbicgPyBbJzUwJScsICc1MSUnXSA6IHRoaXMuaWQgPT09ICdwcm9wb3NhbC1zdGF0aXN0aWNzJyA/IFsnOTQlJywgJzk1JSddIDogdGhpcy5pZCA9PT0gJ3JlcGx5LXR5cGUtcGllJyA/IFsnMSUnLCAnMSUnXSA6IFsnODglJywgJzg5JSddLAogICAgICAgICAgICBjZW50ZXI6IHRoaXMuaWQgPT09ICdjYXRlZ29yeV9kaXN0cmlidXRpb24nID8gWyc1MCUnLCAnMjUlJ10gOiB0aGlzLmlkID09PSAncHJvcG9zYWwtc3RhdGlzdGljcycgPyBbJzIyJScsICc1MCUnXSA6IHRoaXMuaWQgPT09ICdyZXBseS10eXBlLXBpZScgPyBbJzUwJScsICc1MCUnXSA6IFsnMzAlJywgJzUwJSddLAogICAgICAgICAgICBkYXRhOiBbCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgdmFsdWU6IDEwMCwKICAgICAgICAgICAgICAgIGl0ZW1TdHlsZTogewogICAgICAgICAgICAgICAgICBjb2xvcjogJyMyZjY4OWEnCiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfQogICAgICAgICAgICBdLAogICAgICAgICAgICBsYWJlbDogewogICAgICAgICAgICAgIHNob3c6IGZhbHNlCiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICBdLAogICAgICAgIGdyYXBoaWM6IFsKICAgICAgICAgIHsKICAgICAgICAgICAgdHlwZTogJ2NpcmNsZScsCiAgICAgICAgICAgIGxlZnQ6IHRoaXMuaWQgPT09ICdjYXRlZ29yeV9kaXN0cmlidXRpb24nID8gJzM2LjUlJyA6IHRoaXMuaWQgPT09ICdwcm9wb3NhbC1zdGF0aXN0aWNzJyA/ICcxMiUnIDogdGhpcy5pZCA9PT0gJ3JlcGx5LXR5cGUtcGllJyA/ICc0MCUnIDogJzE3JScsCiAgICAgICAgICAgIHRvcDogdGhpcy5pZCA9PT0gJ2NhdGVnb3J5X2Rpc3RyaWJ1dGlvbicgPyAnMTMlJyA6IHRoaXMuaWQgPT09ICdwcm9wb3NhbC1zdGF0aXN0aWNzJyA/ICcyMyUnIDogdGhpcy5pZCA9PT0gJ3JlcGx5LXR5cGUtcGllJyA/ICczNSUnIDogJzI2JScsCiAgICAgICAgICAgIHNoYXBlOiB7CiAgICAgICAgICAgICAgY3g6IDAsCiAgICAgICAgICAgICAgY3k6IDAsCiAgICAgICAgICAgICAgcjogdGhpcy5pZCA9PT0gJ2NhdGVnb3J5X2Rpc3RyaWJ1dGlvbicgPyA2MCA6IHRoaXMuaWQgPT09ICdwcm9wb3NhbC1zdGF0aXN0aWNzJyA/IDQwIDogdGhpcy5pZCA9PT0gJ3JlcGx5LXR5cGUtcGllJyA/IDAgOiA1MAogICAgICAgICAgICB9LAogICAgICAgICAgICBzdHlsZTogewogICAgICAgICAgICAgIGZpbGw6ICdub25lJywKICAgICAgICAgICAgICBsaW5lV2lkdGg6IHRoaXMuaWQgPT09ICdjYXRlZ29yeV9kaXN0cmlidXRpb24nID8gNCA6IDMsCiAgICAgICAgICAgICAgc3Ryb2tlOiB7CiAgICAgICAgICAgICAgICB0eXBlOiAnbGluZWFyJywKICAgICAgICAgICAgICAgIHg6IDAsCiAgICAgICAgICAgICAgICB5OiAwLAogICAgICAgICAgICAgICAgeDI6IDAsCiAgICAgICAgICAgICAgICB5MjogMSwKICAgICAgICAgICAgICAgIGNvbG9yU3RvcHM6IFsKICAgICAgICAgICAgICAgICAgeyBvZmZzZXQ6IDAsIGNvbG9yOiAnIzIzRTFGRicgfSwKICAgICAgICAgICAgICAgICAgeyBvZmZzZXQ6IDEsIGNvbG9yOiAncmdiYSgzNSwyMjUsMjU1LDApJyB9CiAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9LAogICAgICAgICAgICB6OiAxMCwKICAgICAgICAgICAgc2lsZW50OiB0cnVlLAogICAgICAgICAgICBwb3NpdGlvbjogWzAsIDBdCiAgICAgICAgICB9CiAgICAgICAgXQogICAgICB9CiAgICAgIHRoaXMuY2hhcnQuc2V0T3B0aW9uKG9wdGlvbikKICAgICAgLy8g55uR5ZCs56qX5Y+j5aSn5bCP5Y+Y5YyWCiAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdyZXNpemUnLCAoKSA9PiB7CiAgICAgICAgaWYgKHRoaXMuY2hhcnQpIHsKICAgICAgICAgIHRoaXMuY2hhcnQucmVzaXplKCkKICAgICAgICB9CiAgICAgIH0pCiAgICAgIC8vIOWQr+WKqOiHquWKqOmrmOS6ruaViOaenAogICAgICB0aGlzLnN0YXJ0QXV0b0hpZ2hsaWdodCgpCiAgICAgIC8vIOa3u+WKoOm8oOagh+S6i+S7tuebkeWQrAogICAgICB0aGlzLmNoYXJ0Lm9uKCdtb3VzZW92ZXInLCAoKSA9PiB7CiAgICAgICAgdGhpcy5zdG9wQXV0b0hpZ2hsaWdodCgpCiAgICAgIH0pCiAgICAgIHRoaXMuY2hhcnQub24oJ21vdXNlb3V0JywgKCkgPT4gewogICAgICAgIHRoaXMuc3RhcnRBdXRvSGlnaGxpZ2h0KCkKICAgICAgfSkKICAgIH0sCgogICAgLy8g5byA5aeL6Ieq5Yqo6auY5Lqu5pWI5p6cCiAgICBzdGFydEF1dG9IaWdobGlnaHQgKCkgewogICAgICBpZiAodGhpcy5jaGFydERhdGEubGVuZ3RoID09PSAwKSByZXR1cm4KCiAgICAgIHRoaXMuYXV0b0hpZ2hsaWdodFRpbWVyID0gc2V0SW50ZXJ2YWwoKCkgPT4gewogICAgICAgIC8vIOWPlua2iOW9k+WJjemrmOS6ruWSjHRvb2x0aXAKICAgICAgICBpZiAodGhpcy5jdXJyZW50SGlnaGxpZ2h0SW5kZXggPj0gMCkgewogICAgICAgICAgdGhpcy5jaGFydC5kaXNwYXRjaEFjdGlvbih7CiAgICAgICAgICAgIHR5cGU6ICdkb3ducGxheScsCiAgICAgICAgICAgIHNlcmllc0luZGV4OiAwLAogICAgICAgICAgICBkYXRhSW5kZXg6IHRoaXMuY3VycmVudEhpZ2hsaWdodEluZGV4CiAgICAgICAgICB9KQogICAgICAgICAgLy8g6ZqQ6JePdG9vbHRpcAogICAgICAgICAgdGhpcy5jaGFydC5kaXNwYXRjaEFjdGlvbih7CiAgICAgICAgICAgIHR5cGU6ICdoaWRlVGlwJwogICAgICAgICAgfSkKICAgICAgICB9CgogICAgICAgIC8vIOmrmOS6ruS4i+S4gOS4quaVsOaNrumhuQogICAgICAgIHRoaXMuY3VycmVudEhpZ2hsaWdodEluZGV4ID0gKHRoaXMuY3VycmVudEhpZ2hsaWdodEluZGV4ICsgMSkgJSB0aGlzLmNoYXJ0RGF0YS5sZW5ndGgKICAgICAgICB0aGlzLmNoYXJ0LmRpc3BhdGNoQWN0aW9uKHsKICAgICAgICAgIHR5cGU6ICdoaWdobGlnaHQnLAogICAgICAgICAgc2VyaWVzSW5kZXg6IDAsCiAgICAgICAgICBkYXRhSW5kZXg6IHRoaXMuY3VycmVudEhpZ2hsaWdodEluZGV4CiAgICAgICAgfSkKCiAgICAgICAgLy8g5pi+56S6dG9vbHRpcAogICAgICAgIHRoaXMuY2hhcnQuZGlzcGF0Y2hBY3Rpb24oewogICAgICAgICAgdHlwZTogJ3Nob3dUaXAnLAogICAgICAgICAgc2VyaWVzSW5kZXg6IDAsCiAgICAgICAgICBkYXRhSW5kZXg6IHRoaXMuY3VycmVudEhpZ2hsaWdodEluZGV4CiAgICAgICAgfSkKICAgICAgfSwgMjAwMCkgLy8g5q+PMuenkuWIh+aNouS4gOasoQogICAgfSwKCiAgICAvLyDlgZzmraLoh6rliqjpq5jkuq7mlYjmnpwKICAgIHN0b3BBdXRvSGlnaGxpZ2h0ICgpIHsKICAgICAgaWYgKHRoaXMuYXV0b0hpZ2hsaWdodFRpbWVyKSB7CiAgICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLmF1dG9IaWdobGlnaHRUaW1lcikKICAgICAgICB0aGlzLmF1dG9IaWdobGlnaHRUaW1lciA9IG51bGwKICAgICAgfQogICAgICAvLyDlj5bmtojmiYDmnInpq5jkuq4KICAgICAgaWYgKHRoaXMuY2hhcnQgJiYgdGhpcy5jdXJyZW50SGlnaGxpZ2h0SW5kZXggPj0gMCkgewogICAgICAgIHRoaXMuY2hhcnQuZGlzcGF0Y2hBY3Rpb24oewogICAgICAgICAgdHlwZTogJ2Rvd25wbGF5JywKICAgICAgICAgIHNlcmllc0luZGV4OiAwLAogICAgICAgICAgZGF0YUluZGV4OiB0aGlzLmN1cnJlbnRIaWdobGlnaHRJbmRleAogICAgICAgIH0pCiAgICAgICAgdGhpcy5jdXJyZW50SGlnaGxpZ2h0SW5kZXggPSAtMQogICAgICB9CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["PieChart.vue"], "names": [], "mappings": ";AAKA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PieChart.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div :id=\"id\" class=\"pie-chart\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'PieC<PERSON>',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    name: {\n      type: String,\n      required: true\n    },\n    legendIcon: {\n      type: String,\n      default: '',\n      required: false\n    },\n    legendItemGap: {\n      type: Number,\n      default: 25,\n      required: true\n    },\n    legendItemWidth: {\n      type: Number,\n      default: 5,\n      required: true\n    },\n    legendItemHeight: {\n      type: Number,\n      default: 5,\n      required: true\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    }\n  },\n  data () {\n    return {\n      chart: null,\n      autoHighlightTimer: null, // 自动高亮定时器\n      currentHighlightIndex: -1, // 当前高亮的数据项索引\n      // 预定义的颜色数组，按顺序分配给数据项\n      colors: [\n        '#4FC3F7', '#FFF67C', '#66BB6A', '#FFA726',\n        '#FF7043', '#AB47BC', '#5C6BC0', '#42A5F5',\n        '#FFCA28', '#4CAF50', '#EF5350', '#8D6E63',\n        '#9C27B0', '#3F51B5', '#2196F3', '#00BCD4',\n        '#FF9800', '#795548', '#607D8B', '#E91E63'\n      ]\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    // 清除自动高亮定时器\n    if (this.autoHighlightTimer) {\n      clearInterval(this.autoHighlightTimer)\n    }\n    if (this.chart) {\n      this.chart.dispose()\n    }\n  },\n  methods: {\n    getColor (index, item) {\n      // 如果数据项中有颜色信息，优先使用数据中的颜色\n      if (item && item.color) {\n        return item.color\n      }\n      // 否则使用预定义的颜色数组\n      return this.colors[index % this.colors.length]\n    },\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      const option = {\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)',\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          borderColor: '#00d4ff',\n          borderWidth: 1,\n          textStyle: {\n            color: '#fff'\n          }\n        },\n        legend: {\n          orient: this.id === 'category_distribution' ? 'horizontal' : 'vertical',\n          right: this.id === 'reply-type-pie' ? '0%' : this.id === 'category_distribution' ? null : this.id === 'proposal-statistics' ? '0%' : '5%',\n          left: this.id === 'category_distribution' ? 'center' : null,\n          top: this.id === 'reply-type-pie' ? '38%' : this.id === 'category_distribution' ? null : 'center',\n          bottom: this.id === 'category_distribution' ? 20 : null,\n          itemWidth: this.legendItemWidth,\n          itemHeight: this.legendItemHeight,\n          icon: this.legendIcon,\n          itemGap: this.legendItemGap,\n          // itemGap: this.id === 'reply-type-pie' ? 40 : this.id === 'category_distribution' ? 30 : this.id === 'proposal-statistics' ? 12 : 25,\n          textStyle: {\n            color: '#fff',\n            fontSize: 12,\n            fontFamily: 'Microsoft YaHei'\n          },\n          formatter: (name) => {\n            const item = this.chartData.find(d => d.name === name)\n            if (this.id === 'reply-type-pie') {\n              return `${name}`\n            } else {\n              return `${name}  ${item ? item.value : ''}%`\n            }\n          }\n        },\n        series: [\n          {\n            name: this.name,\n            type: 'pie',\n            radius: this.id === 'category_distribution' ? ['30%', '45%'] : this.id === 'proposal-statistics' ? ['60%', '85%'] : this.id === 'reply-type-pie' ? ['40%', '70%'] : ['55%', '80%'],\n            center: this.id === 'category_distribution' ? ['50%', '25%'] : this.id === 'proposal-statistics' ? ['22%', '50%'] : this.id === 'reply-type-pie' ? ['50%', '50%'] : ['30%', '50%'],\n            avoidLabelOverlap: false,\n            emphasis: {\n              scale: true,\n              scaleSize: 10\n            },\n            label: {\n              show: true,\n              position: 'center',\n              fontSize: 16,\n              color: '#fff',\n              formatter: this.name\n            },\n            labelLine: {\n              show: false\n            },\n            itemStyle: {\n              borderWidth: 3,\n              borderColor: '#07345F' // 用大屏背景色\n            },\n            data: this.chartData.map((item, index) => ({\n              value: item.value,\n              name: item.name,\n              itemStyle: { color: this.getColor(index, item) }\n            }))\n          },\n          {\n            type: 'pie',\n            radius: this.id === 'category_distribution' ? ['50%', '51%'] : this.id === 'proposal-statistics' ? ['94%', '95%'] : this.id === 'reply-type-pie' ? ['1%', '1%'] : ['88%', '89%'],\n            center: this.id === 'category_distribution' ? ['50%', '25%'] : this.id === 'proposal-statistics' ? ['22%', '50%'] : this.id === 'reply-type-pie' ? ['50%', '50%'] : ['30%', '50%'],\n            data: [\n              {\n                value: 100,\n                itemStyle: {\n                  color: '#2f689a'\n                }\n              }\n            ],\n            label: {\n              show: false\n            }\n          }\n        ],\n        graphic: [\n          {\n            type: 'circle',\n            left: this.id === 'category_distribution' ? '36.5%' : this.id === 'proposal-statistics' ? '12%' : this.id === 'reply-type-pie' ? '40%' : '17%',\n            top: this.id === 'category_distribution' ? '13%' : this.id === 'proposal-statistics' ? '23%' : this.id === 'reply-type-pie' ? '35%' : '26%',\n            shape: {\n              cx: 0,\n              cy: 0,\n              r: this.id === 'category_distribution' ? 60 : this.id === 'proposal-statistics' ? 40 : this.id === 'reply-type-pie' ? 0 : 50\n            },\n            style: {\n              fill: 'none',\n              lineWidth: this.id === 'category_distribution' ? 4 : 3,\n              stroke: {\n                type: 'linear',\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [\n                  { offset: 0, color: '#23E1FF' },\n                  { offset: 1, color: 'rgba(35,225,255,0)' }\n                ]\n              }\n            },\n            z: 10,\n            silent: true,\n            position: [0, 0]\n          }\n        ]\n      }\n      this.chart.setOption(option)\n      // 监听窗口大小变化\n      window.addEventListener('resize', () => {\n        if (this.chart) {\n          this.chart.resize()\n        }\n      })\n      // 启动自动高亮效果\n      this.startAutoHighlight()\n      // 添加鼠标事件监听\n      this.chart.on('mouseover', () => {\n        this.stopAutoHighlight()\n      })\n      this.chart.on('mouseout', () => {\n        this.startAutoHighlight()\n      })\n    },\n\n    // 开始自动高亮效果\n    startAutoHighlight () {\n      if (this.chartData.length === 0) return\n\n      this.autoHighlightTimer = setInterval(() => {\n        // 取消当前高亮和tooltip\n        if (this.currentHighlightIndex >= 0) {\n          this.chart.dispatchAction({\n            type: 'downplay',\n            seriesIndex: 0,\n            dataIndex: this.currentHighlightIndex\n          })\n          // 隐藏tooltip\n          this.chart.dispatchAction({\n            type: 'hideTip'\n          })\n        }\n\n        // 高亮下一个数据项\n        this.currentHighlightIndex = (this.currentHighlightIndex + 1) % this.chartData.length\n        this.chart.dispatchAction({\n          type: 'highlight',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n\n        // 显示tooltip\n        this.chart.dispatchAction({\n          type: 'showTip',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n      }, 2000) // 每2秒切换一次\n    },\n\n    // 停止自动高亮效果\n    stopAutoHighlight () {\n      if (this.autoHighlightTimer) {\n        clearInterval(this.autoHighlightTimer)\n        this.autoHighlightTimer = null\n      }\n      // 取消所有高亮\n      if (this.chart && this.currentHighlightIndex >= 0) {\n        this.chart.dispatchAction({\n          type: 'downplay',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n        this.currentHighlightIndex = -1\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.pie-chart {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n</style>\n"]}]}