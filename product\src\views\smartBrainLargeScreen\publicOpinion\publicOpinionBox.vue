<template>
  <div class="big-screen" ref="bigScreen">
    <div class="screen-header">
      <div class="header-left">
        <span class="date-time">{{ currentTime }}</span>
        <span class="weather">晴 24℃ 东南风</span>
      </div>
      <div class="header-center">
        <img src="../../../assets/largeScreen/top_header_txt.png" alt="" style="height: 50px;">
      </div>
      <div class="header-right">
        <div class="header-buttons">
          <div class="header-btn current-module-btn">
            <span>社情民意</span>
          </div>
          <div class="header-btn home-btn" @click="goHome">
            <span>返回首页</span>
          </div>
        </div>
      </div>
    </div>
    <div class="screen-content">
      <div class="left-panel">
        <!-- 采用情况 -->
        <div class="social_adoption_scenario">
          <div class="header_box">
            <span class="header_text_left">采用情况</span>
          </div>
          <div class="social_adoption_scenario_content">
          </div>
        </div>
        <!-- 类型分析 -->
        <div class="social_type_analysis">
          <div class="header_box">
            <span class="header_text_left">类型分析</span>
          </div>
          <div class="social_type_analysis_content">
            <PieChart id="social_type" :chart-data="socialTypeChartData" :name="socialTypeChartName"
              :legendItemWidth="13" :legendItemHeight="13" :radius="['48%', '70%']" :center="['30%', '50%']"
              :lineRadius="['78%', '79%']" :lineCenter="['30%', '50%']" graphicLeft="18.2%" graphicTop="28%"
              :graphicShapeR="55" />
          </div>
        </div>
      </div>
      <div class="center-panel">
        <!-- 社情民意整体情况 -->
        <div class="social_overall_situation">
          <div class="header_box">
            <span class="header_text_left">社情民意整体情况</span>
          </div>
          <div class="social_overall_situation_content">
          </div>
        </div>
        <!-- 各单位报送情况 -->
        <div class="each_unit_submits">
          <div class="header_box">
            <span class="header_text_left">各单位报送情况</span>
          </div>
          <div class="each_unit_submits_content">
          </div>
        </div>
      </div>
      <div class="right-panel">
        <!-- 批示情况 -->
        <div class="approval_status">
          <div class="header_box">
            <span class="header_text_left">批示情况</span>
          </div>
          <div class="approval_status_content">
          </div>
        </div>
        <!-- 个人报送和采用情况 -->
        <div class="personal_adoption">
          <div class="header_box">
            <span class="header_text_left">个人报送和采用情况</span>
          </div>
          <div class="personal_adoption_content">
            <div class="personal-table-container">
              <div class="table-header">
                <div class="header-cell name-cell">姓名</div>
                <div class="header-cell submit-cell">报送数量</div>
                <div class="header-cell adopt-cell">采用数量</div>
              </div>
              <div class="table-body">
                <div v-for="(item, index) in personalAdoptionData" :key="index" class="table-row">
                  <div class="body-cell name-cell">
                    <div class="avatar-container">
                      <img :src="item.avatar" :alt="item.name" class="avatar">
                      <span class="name">{{ item.name }}</span>
                    </div>
                  </div>
                  <div class="body-cell submit-cell">
                    <span class="submit-count">{{ item.submitCount }}</span>
                  </div>
                  <div class="body-cell adopt-cell">
                    <span class="adopt-count">{{ item.adoptCount }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 热词分析 -->
        <div class="hot_word_analysis">
          <div class="header_box">
            <span class="header_text_left">热词分析</span>
            <span class="header_text_right"></span>
          </div>
          <div class="hot_word_analysis_content">
            <WordCloud chart-id="hotWordChart" :words="hotWordsData" @word-click="onWordClick" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useIndex } from '../screen.js'
import WordCloud from '../components/WordCloud.vue'
import PieChart from '../components/PieChart.vue'
export default {
  name: 'BigScreen',
  components: {
    WordCloud,
    PieChart
  },
  data () {
    return {
      currentTime: '',
      hotWordsData: [
        { name: '经济建设', value: 10 },
        { name: '人才培养', value: 9 },
        { name: 'AI技术', value: 6 },
        { name: '改革创新', value: 7 },
        { name: '教育', value: 5 },
        { name: '车辆交通', value: 6 },
        { name: '旅游', value: 5 },
        { name: '公共安全', value: 7 },
        { name: '智能化', value: 6 },
        { name: '电梯故障', value: 4 },
        { name: '社会保障', value: 6 },
        { name: '环境保护', value: 5 },
        { name: '医疗卫生', value: 7 },
        { name: '文化建设', value: 4 },
        { name: '科技创新', value: 8 }
      ],
      // 类型分析
      socialTypeChartData: [
        { name: '社会', value: 20, percentage: '5%' },
        { name: '政治', value: 125, percentage: '30%' },
        { name: '经济', value: 168, percentage: '40%' },
        { name: '文化', value: 85, percentage: '20%' },
        { name: '生态文明', value: 20, percentage: '5%' }
      ],
      socialTypeChartName: '类别分析',
      // 个人报送和采用情况数据
      personalAdoptionData: [
        { name: '马波', avatar: require('@/assets/images/man-head.png'), submitCount: 12, adoptCount: 3 },
        { name: '王玉明', avatar: require('@/assets/images/woman-head.png'), submitCount: 11, adoptCount: 2 },
        { name: '王忠', avatar: require('@/assets/images/man-head.png'), submitCount: 11, adoptCount: 2 },
        { name: '汪杨波', avatar: require('@/assets/images/man-head.png'), submitCount: 10, adoptCount: 2 },
        { name: '黄毅', avatar: require('@/assets/images/man-head.png'), submitCount: 8, adoptCount: 1 },
        { name: '李明', avatar: require('@/assets/images/man-head.png'), submitCount: 7, adoptCount: 1 },
        { name: '张三', avatar: require('@/assets/images/man-head.png'), submitCount: 6, adoptCount: 1 },
        { name: '李四', avatar: require('@/assets/images/woman-head.png'), submitCount: 5, adoptCount: 0 }
      ]
    }
  },
  computed: {
  },
  mounted () {
    this.initScreen()
    this.updateTime()
    this.timeInterval = setInterval(this.updateTime, 1000)
  },
  beforeDestroy () {
    if (this.timeInterval) {
      clearInterval(this.timeInterval)
    }
  },
  methods: {
    initScreen () {
      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)
      calcRate()
      windowDraw()
    },
    updateTime () {
      const now = new Date()
      this.currentTime = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    // 返回首页
    goHome () {
      this.$router.push({ path: '/homeBox' })
    },
    // 词云点击事件
    onWordClick (word) {
      console.log('词汇点击:', word)
    }
  }
}
</script>

<style lang="scss" scoped>
.big-screen {
  width: 1920px;
  height: 1080px;
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transform-origin: left top;
  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;
  background-size: cover;
  background-position: center;

  .screen-header {
    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;
    background-size: 100% 100%;
    background-position: center;
    height: 65px;
    display: flex;
    align-items: center;
    padding: 0 40px;

    .header-left {
      display: flex;
      gap: 20px;
      font-size: 14px;
      color: #8cc8ff;
      flex: 1;
    }

    .header-center {
      width: 60%;
      text-align: center;
    }

    .header-right {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .header-buttons {
        display: flex;
        gap: 15px;

        .header-btn {
          height: 42px;
          line-height: 42px;
          padding: 0 16px;
          cursor: pointer;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
          }

          &:hover {
            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);
            border-color: rgba(0, 181, 254, 1);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);

            &::before {
              left: 100%;
            }
          }

          &.current-module-btn {
            background: url('../../../assets/largeScreen/icon_committee.png') no-repeat;
            background-size: 100% 100%;
            background-position: center;
            font-weight: bold;
            font-size: 16px;
            color: #FFFFFF;
          }

          &.home-btn {
            background: url('../../../assets/largeScreen/icon_back_home.png') no-repeat;
            background-size: 100% 100%;
            background-position: center;
            font-weight: 400;
            font-size: 16px;
            color: #1FC6FF;
          }

          &.area-select-btn {
            background: linear-gradient(135deg, rgba(0, 181, 254, 0.2) 0%, rgba(0, 120, 220, 0.2) 100%);
            border: 1px solid rgba(0, 181, 254, 0.5);
            border-radius: 6px;
            font-weight: 500;
            font-size: 16px;
            color: #1FC6FF;
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-width: 120px;

            .dropdown-icon {
              margin-left: 8px;
              font-size: 12px;
              transition: transform 0.3s ease;
              color: #1FC6FF;

              &.active {
                transform: rotate(180deg);
              }
            }

            &:hover {
              background: linear-gradient(135deg, rgba(0, 181, 254, 0.3) 0%, rgba(0, 120, 220, 0.3) 100%);
              border-color: rgba(0, 181, 254, 0.8);
            }
          }
        }
      }
    }
  }

  .screen-content {
    height: calc(100% - 65px);
    display: flex;
    padding: 35px 20px 0 20px;
    gap: 30px;

    .header_box {
      position: absolute;
      top: 15px;
      left: 24px;
      right: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .header_text_left {
        font-weight: bold;
        font-size: 20px;
        color: #FFFFFF;
        cursor: pointer;
      }

      .header_text_right {
        font-size: 15px;
        color: #FFD600;
      }

      .header_text_center {
        font-size: 15px;
        color: #FFFFFF;
        display: flex;
        align-items: center;

        span {
          font-weight: 500;
          font-size: 24px;
          color: #02FBFB;
          margin: 0 10px 0 6px;
        }
      }
    }

    .left-panel,
    .right-panel {
      width: 490px;
      display: flex;
      flex-direction: column;
      gap: 20px 30px;
    }

    .left-panel {
      .social_adoption_scenario {
        background: url('../../../assets/largeScreen/social_adoption_scenario_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 610px;
        width: 100%;

        .social_adoption_scenario_content {
          height: 100%;
          margin-top: 72px;
          margin-left: 20px;
          margin-right: 20px;
        }
      }

      .social_type_analysis {
        background: url('../../../assets/largeScreen/social_type_analysis_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 300px;
        width: 100%;

        .social_type_analysis_content {
          margin-top: 50px;
          height: calc(100% - 50px);
        }
      }
    }

    .right-panel {
      .approval_status {
        background: url('../../../assets/largeScreen/approval_status_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 230px;
        width: 100%;

        .approval_status_content {
          margin-top: 75px;
          margin-left: 12px;
          margin-right: 12px;
        }
      }

      .personal_adoption {
        background: url('../../../assets/largeScreen/personal_adoption_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 350px;
        width: 100%;

        .personal_adoption_content {
          margin-top: 70px;
          margin-left: 20px;
          margin-right: 20px;
          height: 260px;

          .personal-table-container {
            height: 100%;
            display: flex;
            flex-direction: column;

            .table-header {
              display: flex;
              background: rgba(31, 198, 255, 0.10);
              border-radius: 4px 4px 0 0;
              height: 40px;
              align-items: center;

              .header-cell {
                display: flex;
                align-items: center;
                color: #fff;
                font-size: 14px;

                &.name-cell {
                  flex: 1.2;
                  justify-content: flex-start;
                  padding-left: 20px;
                }

                &.submit-cell,
                &.adopt-cell {
                  flex: 1;
                  justify-content: center;
                }
              }
            }

            .table-body {
              flex: 1;
              overflow-y: auto;

              &::-webkit-scrollbar {
                width: 4px;
              }

              &::-webkit-scrollbar-track {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 2px;
              }

              &::-webkit-scrollbar-thumb {
                background: rgba(31, 198, 255, 0.6);
                border-radius: 2px;

                &:hover {
                  background: rgba(31, 198, 255, 0.8);
                }
              }

              .table-row {
                display: flex;
                height: 50px;
                align-items: center;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);

                &:hover {
                  background: rgba(31, 198, 255, 0.05);
                }

                .body-cell {
                  display: flex;
                  align-items: center;
                  justify-content: center;

                  &.name-cell {
                    flex: 1.2;
                    justify-content: flex-start;
                    padding-left: 15px;

                    .avatar-container {
                      display: flex;
                      align-items: center;

                      .avatar {
                        width: 30px;
                        height: 30px;
                        border-radius: 50%;
                        margin-right: 10px;
                        object-fit: cover;
                      }

                      .name {
                        color: #fff;
                        font-size: 14px;
                      }
                    }
                  }

                  &.submit-cell {
                    flex: 1;

                    .submit-count {
                      color: #1FC6FF;
                      font-size: 16px;
                      font-weight: 600;
                    }
                  }

                  &.adopt-cell {
                    flex: 1;

                    .adopt-count {
                      color: #FFD600;
                      font-size: 16px;
                      font-weight: 600;
                    }
                  }
                }
              }
            }
          }
        }
      }

      .hot_word_analysis {
        background: url('../../../assets/largeScreen/hot_word_analysis_bg3.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 310px;
        width: 100%;

        .hot_word_analysis_content {
          height: calc(100% - 92px);
          margin-top: 72px;
          margin-bottom: 20px;
          position: relative;
        }
      }
    }

    .center-panel {
      flex: 1;
      gap: 20px;
      display: flex;
      flex-direction: column;

      .social_overall_situation {
        background: url('../../../assets/largeScreen/social_overall_situation_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 460px;
        width: 100%;

        .social_overall_situation_content {
          margin-top: 65px;
          margin-left: 16px;
          margin-right: 16px;

        }
      }

      .each_unit_submits {
        background: url('../../../assets/largeScreen/each_unit_submits_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 450px;
        width: 100%;

        .each_unit_submits_content {
          height: 100%;
          margin-top: 65px;
          margin-left: 16px;
          margin-right: 16px;
        }
      }
    }
  }
}
</style>
