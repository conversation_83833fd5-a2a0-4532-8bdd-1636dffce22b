{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\proposalStatistics\\proposalStatisticsBox.vue?vue&type=template&id=778a9eeb&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\proposalStatistics\\proposalStatisticsBox.vue", "mtime": 1756455003202}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1655712169000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}