{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\proposalStatistics\\proposalStatisticsBox.vue?vue&type=template&id=778a9eeb&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\proposalStatistics\\proposalStatisticsBox.vue", "mtime": 1756455988588}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1655712169000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}