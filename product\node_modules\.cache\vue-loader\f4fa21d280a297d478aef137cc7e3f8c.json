{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\publicOpinion\\publicOpinionBox.vue?vue&type=style&index=0&id=4a7ae757&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\publicOpinion\\publicOpinionBox.vue", "mtime": 1756457551625}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["publicOpinionBox.vue"], "names": [], "mappings": ";AAmNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "publicOpinionBox.vue", "sourceRoot": "src/views/smartBrainLargeScreen/publicOpinion", "sourcesContent": ["<template>\r\n  <div class=\"big-screen\" ref=\"bigScreen\">\r\n    <div class=\"screen-header\">\r\n      <div class=\"header-left\">\r\n        <span class=\"date-time\">{{ currentTime }}</span>\r\n        <span class=\"weather\">晴 24℃ 东南风</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <div class=\"header-buttons\">\r\n          <div class=\"header-btn current-module-btn\">\r\n            <span>社情民意</span>\r\n          </div>\r\n          <div class=\"header-btn home-btn\" @click=\"goHome\">\r\n            <span>返回首页</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"screen-content\">\r\n      <div class=\"left-panel\">\r\n        <!-- 采用情况 -->\r\n        <div class=\"social_adoption_scenario\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">采用情况</span>\r\n          </div>\r\n          <div class=\"social_adoption_scenario_content\">\r\n          </div>\r\n        </div>\r\n        <!-- 类型分析 -->\r\n        <div class=\"social_type_analysis\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">类型分析</span>\r\n          </div>\r\n          <div class=\"social_type_analysis_content\">\r\n            <PieChart id=\"social_type\" :chart-data=\"socialTypeChartData\" :name=\"socialTypeChartName\"\r\n              :legendItemWidth=\"13\" :legendItemHeight=\"13\" :radius=\"['48%', '70%']\" :center=\"['30%', '50%']\"\r\n              :lineRadius=\"['78%', '79%']\" :lineCenter=\"['30%', '50%']\" graphicLeft=\"18.2%\" graphicTop=\"28%\"\r\n              :graphicShapeR=\"55\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"center-panel\">\r\n        <!-- 社情民意整体情况 -->\r\n        <div class=\"social_overall_situation\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">社情民意整体情况</span>\r\n          </div>\r\n          <div class=\"social_overall_situation_content\">\r\n          </div>\r\n        </div>\r\n        <!-- 各单位报送情况 -->\r\n        <div class=\"each_unit_submits\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">各单位报送情况</span>\r\n          </div>\r\n          <div class=\"each_unit_submits_content\">\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"right-panel\">\r\n        <!-- 批示情况 -->\r\n        <div class=\"approval_status\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">批示情况</span>\r\n          </div>\r\n          <div class=\"approval_status_content\">\r\n          </div>\r\n        </div>\r\n        <!-- 个人报送和采用情况 -->\r\n        <div class=\"personal_adoption\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">个人报送和采用情况</span>\r\n          </div>\r\n          <div class=\"personal_adoption_content\">\r\n            <div class=\"personal-table-container\">\r\n              <div class=\"table-header\">\r\n                <div class=\"header-cell name-cell\">姓名</div>\r\n                <div class=\"header-cell submit-cell\">报送数量</div>\r\n                <div class=\"header-cell adopt-cell\">采用数量</div>\r\n              </div>\r\n              <div class=\"table-body\">\r\n                <div v-for=\"(item, index) in personalAdoptionData\" :key=\"index\" class=\"table-row\">\r\n                  <div class=\"body-cell name-cell\">\r\n                    <div class=\"avatar-container\">\r\n                      <img :src=\"item.avatar\" :alt=\"item.name\" class=\"avatar\">\r\n                      <span class=\"name\">{{ item.name }}</span>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"body-cell submit-cell\">\r\n                    <span class=\"submit-count\">{{ item.submitCount }}</span>\r\n                  </div>\r\n                  <div class=\"body-cell adopt-cell\">\r\n                    <span class=\"adopt-count\">{{ item.adoptCount }}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 热词分析 -->\r\n        <div class=\"hot_word_analysis\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">热词分析</span>\r\n            <span class=\"header_text_right\"></span>\r\n          </div>\r\n          <div class=\"hot_word_analysis_content\">\r\n            <WordCloud chart-id=\"hotWordChart\" :words=\"hotWordsData\" @word-click=\"onWordClick\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { useIndex } from '../screen.js'\r\nimport WordCloud from '../components/WordCloud.vue'\r\nimport PieChart from '../components/PieChart.vue'\r\nexport default {\r\n  name: 'BigScreen',\r\n  components: {\r\n    WordCloud,\r\n    PieChart\r\n  },\r\n  data () {\r\n    return {\r\n      currentTime: '',\r\n      hotWordsData: [\r\n        { name: '经济建设', value: 10 },\r\n        { name: '人才培养', value: 9 },\r\n        { name: 'AI技术', value: 6 },\r\n        { name: '改革创新', value: 7 },\r\n        { name: '教育', value: 5 },\r\n        { name: '车辆交通', value: 6 },\r\n        { name: '旅游', value: 5 },\r\n        { name: '公共安全', value: 7 },\r\n        { name: '智能化', value: 6 },\r\n        { name: '电梯故障', value: 4 },\r\n        { name: '社会保障', value: 6 },\r\n        { name: '环境保护', value: 5 },\r\n        { name: '医疗卫生', value: 7 },\r\n        { name: '文化建设', value: 4 },\r\n        { name: '科技创新', value: 8 }\r\n      ],\r\n      // 类型分析\r\n      socialTypeChartData: [\r\n        { name: '社会', value: 20, percentage: '5%' },\r\n        { name: '政治', value: 125, percentage: '30%' },\r\n        { name: '经济', value: 168, percentage: '40%' },\r\n        { name: '文化', value: 85, percentage: '20%' },\r\n        { name: '生态文明', value: 20, percentage: '5%' }\r\n      ],\r\n      socialTypeChartName: '类别分析',\r\n      // 个人报送和采用情况数据\r\n      personalAdoptionData: [\r\n        { name: '马波', avatar: require('@/assets/images/man-head.png'), submitCount: 12, adoptCount: 3 },\r\n        { name: '王玉明', avatar: require('@/assets/images/woman-head.png'), submitCount: 11, adoptCount: 2 },\r\n        { name: '王忠', avatar: require('@/assets/images/man-head.png'), submitCount: 11, adoptCount: 2 },\r\n        { name: '汪杨波', avatar: require('@/assets/images/man-head.png'), submitCount: 10, adoptCount: 2 },\r\n        { name: '黄毅', avatar: require('@/assets/images/man-head.png'), submitCount: 8, adoptCount: 1 },\r\n        { name: '李明', avatar: require('@/assets/images/man-head.png'), submitCount: 7, adoptCount: 1 },\r\n        { name: '张三', avatar: require('@/assets/images/man-head.png'), submitCount: 6, adoptCount: 1 },\r\n        { name: '李四', avatar: require('@/assets/images/woman-head.png'), submitCount: 5, adoptCount: 0 }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n  },\r\n  mounted () {\r\n    this.initScreen()\r\n    this.updateTime()\r\n    this.timeInterval = setInterval(this.updateTime, 1000)\r\n  },\r\n  beforeDestroy () {\r\n    if (this.timeInterval) {\r\n      clearInterval(this.timeInterval)\r\n    }\r\n  },\r\n  methods: {\r\n    initScreen () {\r\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\r\n      calcRate()\r\n      windowDraw()\r\n    },\r\n    updateTime () {\r\n      const now = new Date()\r\n      this.currentTime = now.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      })\r\n    },\r\n    // 返回首页\r\n    goHome () {\r\n      this.$router.push({ path: '/homeBox' })\r\n    },\r\n    // 词云点击事件\r\n    onWordClick (word) {\r\n      console.log('词汇点击:', word)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.big-screen {\r\n  width: 1920px;\r\n  height: 1080px;\r\n  position: relative;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transform-origin: left top;\r\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\r\n  background-size: cover;\r\n  background-position: center;\r\n\r\n  .screen-header {\r\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\r\n    background-size: 100% 100%;\r\n    background-position: center;\r\n    height: 65px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 40px;\r\n\r\n    .header-left {\r\n      display: flex;\r\n      gap: 20px;\r\n      font-size: 14px;\r\n      color: #8cc8ff;\r\n      flex: 1;\r\n    }\r\n\r\n    .header-center {\r\n      width: 60%;\r\n      text-align: center;\r\n    }\r\n\r\n    .header-right {\r\n      flex: 1;\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      align-items: center;\r\n\r\n      .header-buttons {\r\n        display: flex;\r\n        gap: 15px;\r\n\r\n        .header-btn {\r\n          height: 42px;\r\n          line-height: 42px;\r\n          padding: 0 16px;\r\n          cursor: pointer;\r\n          transition: all 0.3s ease;\r\n          position: relative;\r\n          overflow: hidden;\r\n\r\n          &::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: -100%;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n            transition: left 0.5s;\r\n          }\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);\r\n            border-color: rgba(0, 181, 254, 1);\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);\r\n\r\n            &::before {\r\n              left: 100%;\r\n            }\r\n          }\r\n\r\n          &.current-module-btn {\r\n            background: url('../../../assets/largeScreen/icon_committee.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: bold;\r\n            font-size: 16px;\r\n            color: #FFFFFF;\r\n          }\r\n\r\n          &.home-btn {\r\n            background: url('../../../assets/largeScreen/icon_back_home.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: 400;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n          }\r\n\r\n          &.area-select-btn {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 0.2) 0%, rgba(0, 120, 220, 0.2) 100%);\r\n            border: 1px solid rgba(0, 181, 254, 0.5);\r\n            border-radius: 6px;\r\n            font-weight: 500;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            min-width: 120px;\r\n\r\n            .dropdown-icon {\r\n              margin-left: 8px;\r\n              font-size: 12px;\r\n              transition: transform 0.3s ease;\r\n              color: #1FC6FF;\r\n\r\n              &.active {\r\n                transform: rotate(180deg);\r\n              }\r\n            }\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(0, 181, 254, 0.3) 0%, rgba(0, 120, 220, 0.3) 100%);\r\n              border-color: rgba(0, 181, 254, 0.8);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .screen-content {\r\n    height: calc(100% - 65px);\r\n    display: flex;\r\n    padding: 35px 20px 0 20px;\r\n    gap: 30px;\r\n\r\n    .header_box {\r\n      position: absolute;\r\n      top: 15px;\r\n      left: 24px;\r\n      right: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n\r\n      .header_text_left {\r\n        font-weight: bold;\r\n        font-size: 20px;\r\n        color: #FFFFFF;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .header_text_right {\r\n        font-size: 15px;\r\n        color: #FFD600;\r\n      }\r\n\r\n      .header_text_center {\r\n        font-size: 15px;\r\n        color: #FFFFFF;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          font-weight: 500;\r\n          font-size: 24px;\r\n          color: #02FBFB;\r\n          margin: 0 10px 0 6px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .left-panel,\r\n    .right-panel {\r\n      width: 490px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 20px 30px;\r\n    }\r\n\r\n    .left-panel {\r\n      .social_adoption_scenario {\r\n        background: url('../../../assets/largeScreen/social_adoption_scenario_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 610px;\r\n        width: 100%;\r\n\r\n        .social_adoption_scenario_content {\r\n          height: 100%;\r\n          margin-top: 72px;\r\n          margin-left: 20px;\r\n          margin-right: 20px;\r\n        }\r\n      }\r\n\r\n      .social_type_analysis {\r\n        background: url('../../../assets/largeScreen/social_type_analysis_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 300px;\r\n        width: 100%;\r\n\r\n        .social_type_analysis_content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n    }\r\n\r\n    .right-panel {\r\n      .approval_status {\r\n        background: url('../../../assets/largeScreen/approval_status_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 230px;\r\n        width: 100%;\r\n\r\n        .approval_status_content {\r\n          margin-top: 75px;\r\n          margin-left: 12px;\r\n          margin-right: 12px;\r\n        }\r\n      }\r\n\r\n      .personal_adoption {\r\n        background: url('../../../assets/largeScreen/personal_adoption_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 350px;\r\n        width: 100%;\r\n\r\n        .personal_adoption_content {\r\n          margin-top: 70px;\r\n          margin-left: 20px;\r\n          margin-right: 20px;\r\n          height: 260px;\r\n\r\n          .personal-table-container {\r\n            height: 100%;\r\n            display: flex;\r\n            flex-direction: column;\r\n\r\n            .table-header {\r\n              display: flex;\r\n              background: rgba(31, 198, 255, 0.10);\r\n              border-radius: 4px 4px 0 0;\r\n              height: 40px;\r\n              align-items: center;\r\n\r\n              .header-cell {\r\n                display: flex;\r\n                align-items: center;\r\n                color: #fff;\r\n                font-size: 14px;\r\n\r\n                &.name-cell {\r\n                  flex: 1.2;\r\n                  justify-content: flex-start;\r\n                  padding-left: 20px;\r\n                }\r\n\r\n                &.submit-cell,\r\n                &.adopt-cell {\r\n                  flex: 1;\r\n                  justify-content: center;\r\n                }\r\n              }\r\n            }\r\n\r\n            .table-body {\r\n              flex: 1;\r\n              overflow-y: auto;\r\n\r\n              &::-webkit-scrollbar {\r\n                width: 4px;\r\n              }\r\n\r\n              &::-webkit-scrollbar-track {\r\n                background: rgba(255, 255, 255, 0.1);\r\n                border-radius: 2px;\r\n              }\r\n\r\n              &::-webkit-scrollbar-thumb {\r\n                background: rgba(31, 198, 255, 0.6);\r\n                border-radius: 2px;\r\n\r\n                &:hover {\r\n                  background: rgba(31, 198, 255, 0.8);\r\n                }\r\n              }\r\n\r\n              .table-row {\r\n                display: flex;\r\n                height: 50px;\r\n                align-items: center;\r\n                border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n\r\n                &:hover {\r\n                  background: rgba(31, 198, 255, 0.05);\r\n                }\r\n\r\n                .body-cell {\r\n                  display: flex;\r\n                  align-items: center;\r\n                  justify-content: center;\r\n\r\n                  &.name-cell {\r\n                    flex: 1.2;\r\n                    justify-content: flex-start;\r\n                    padding-left: 15px;\r\n\r\n                    .avatar-container {\r\n                      display: flex;\r\n                      align-items: center;\r\n\r\n                      .avatar {\r\n                        width: 30px;\r\n                        height: 30px;\r\n                        border-radius: 50%;\r\n                        margin-right: 10px;\r\n                        object-fit: cover;\r\n                      }\r\n\r\n                      .name {\r\n                        color: #fff;\r\n                        font-size: 14px;\r\n                      }\r\n                    }\r\n                  }\r\n\r\n                  &.submit-cell {\r\n                    flex: 1;\r\n\r\n                    .submit-count {\r\n                      color: #1FC6FF;\r\n                      font-size: 16px;\r\n                      font-weight: 600;\r\n                    }\r\n                  }\r\n\r\n                  &.adopt-cell {\r\n                    flex: 1;\r\n\r\n                    .adopt-count {\r\n                      color: #FFD600;\r\n                      font-size: 16px;\r\n                      font-weight: 600;\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .hot_word_analysis {\r\n        background: url('../../../assets/largeScreen/hot_word_analysis_bg3.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 310px;\r\n        width: 100%;\r\n\r\n        .hot_word_analysis_content {\r\n          height: calc(100% - 92px);\r\n          margin-top: 72px;\r\n          margin-bottom: 20px;\r\n          position: relative;\r\n        }\r\n      }\r\n    }\r\n\r\n    .center-panel {\r\n      flex: 1;\r\n      gap: 20px;\r\n      display: flex;\r\n      flex-direction: column;\r\n\r\n      .social_overall_situation {\r\n        background: url('../../../assets/largeScreen/social_overall_situation_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 460px;\r\n        width: 100%;\r\n\r\n        .social_overall_situation_content {\r\n          margin-top: 65px;\r\n          margin-left: 16px;\r\n          margin-right: 16px;\r\n\r\n        }\r\n      }\r\n\r\n      .each_unit_submits {\r\n        background: url('../../../assets/largeScreen/each_unit_submits_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 450px;\r\n        width: 100%;\r\n\r\n        .each_unit_submits_content {\r\n          height: 100%;\r\n          margin-top: 65px;\r\n          margin-left: 16px;\r\n          margin-right: 16px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}