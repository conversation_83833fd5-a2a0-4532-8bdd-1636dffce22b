{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\home\\homeBox.vue?vue&type=style&index=0&id=76285fab&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\home\\homeBox.vue", "mtime": 1756454249558}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["homeBox.vue"], "names": [], "mappings": ";AAiaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "homeBox.vue", "sourceRoot": "src/views/smartBrainLargeScreen/home", "sourcesContent": ["<template>\n  <div class=\"big-screen\" ref=\"bigScreen\">\n    <div class=\"screen-header\">\n      <div class=\"header-left\">\n        <span class=\"date-time\">{{ currentTime }}</span>\n        <span class=\"weather\">晴 24℃ 东南风</span>\n      </div>\n      <div class=\"header-center\">\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\n      </div>\n      <div class=\"header-right\"></div>\n    </div>\n    <div class=\"screen-content\">\n      <div class=\"left-panel\">\n        <!-- 委员统计 -->\n        <div class=\"committee_statistics\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\" @click=\"handleCommitteeClick\">委员统计</span>\n            <span class=\"header_text_right\">十二届二次</span>\n          </div>\n          <div class=\"committee_statistics_content\">\n            <div class=\"committee_statistics_num\">\n              <div v-for=\"(item, index) in committeeStatisticsNum\" :key=\"index\" class=\"num_box\">\n                <img :src=\"item.icon\" alt=\"\" class=\"num_icon\">\n                <div>\n                  <div class=\"num_label\">{{ item.label }}</div>\n                  <div class=\"num_value\" :style=\"`color:${item.color}`\">{{ item.value }}</div>\n                </div>\n              </div>\n            </div>\n            <div class=\"committee_statistics_chart\">\n              <BarScrollChart id=\"committee-statistics\" :showCount=\"5\" :chart-data=\"committeeBarData\" />\n            </div>\n          </div>\n        </div>\n        <!-- 提案统计 -->\n        <div class=\"proposal_statistics\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\" @click=\"handleProposalClick\">提案统计</span>\n            <span class=\"header_text_right\">十二届二次会议</span>\n            <span class=\"header_text_center\">提交提案总数：<span>873</span>件</span>\n          </div>\n          <div class=\"proposal_statistics_content\">\n            <div class=\"proposal_statistics_num\">\n              <div v-for=\"(item, index) in proposalStatisticsNum\" :key=\"index\" class=\"num_box\">\n                <img :src=\"item.icon\" alt=\"\" class=\"num_icon\">\n                <div>\n                  <div class=\"num_label\">{{ item.label }}</div>\n                  <div class=\"num_value\" :style=\"`color:${item.color}`\">{{ item.value }}<span class=\"num_unit\">{{\n                    item.unit }}</span></div>\n                </div>\n              </div>\n            </div>\n            <div class=\"proposal_statistics_chart\">\n              <PieChart id=\"proposal-statistics\" :chart-data=\"proposalChartData\" :name=\"proposalChartName\"\n                legendIcon=\"circle\" :legendItemGap=\"12\" />\n            </div>\n          </div>\n        </div>\n        <!-- 工作动态 -->\n        <div class=\"work_dynamics\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\">工作动态</span>\n            <span class=\"header_text_right\">本年</span>\n          </div>\n          <div class=\"work_dynamics_content\">\n            <div class=\"dynamics-list\">\n              <div v-for=\"(item, index) in workDynamicsData\" :key=\"item.id\" class=\"dynamics-item\"\n                :class=\"{ 'with-bg-image': index % 2 === 0, 'with-bg-color': index % 2 === 1 }\">\n                <div class=\"dynamics-content\">\n                  <div class=\"dynamics-title\">{{ item.title }}</div>\n                  <div class=\"dynamics-date\">{{ item.date }}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div class=\"center-panel\">\n        <!-- 地图 -->\n        <div class=\"map_box\">\n          <MapComponent :data=\"mapData\" :areaId=\"areaId + ''\" :areaName=\"areaName\" @region-click=\"handleRegionClick\" />\n        </div>\n        <!-- 履职统计 -->\n        <div class=\"performance_statistics\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\" @click=\"handlePerformanceClick\">履职统计</span>\n            <span class=\"header_text_right\">十二届二次</span>\n          </div>\n          <div class=\"performance_statistics_content\">\n            <div class=\"table-container\">\n              <!-- 固定表头 -->\n              <div class=\"table-header\">\n                <div class=\"header-cell\">姓名</div>\n                <div class=\"header-cell\">会议活动</div>\n                <div class=\"header-cell\">政协提案</div>\n                <div class=\"header-cell\">社情民意</div>\n                <div class=\"header-cell\">议政建言</div>\n                <div class=\"header-cell\">读书心得</div>\n                <div class=\"header-cell\">委员培训</div>\n                <div class=\"header-cell\"></div> <!-- 滚动条占位 -->\n              </div>\n              <!-- 可滚动内容 -->\n              <div class=\"table-body\">\n                <div class=\"table-row\" v-for=\"(item, index) in performanceData\" :key=\"index\">\n                  <div class=\"table-cell name-col\">{{ item.name }}</div>\n                  <div class=\"table-cell meeting-col\">{{ item.meeting }}</div>\n                  <div class=\"table-cell proposal-col\">{{ item.proposal }}</div>\n                  <div class=\"table-cell opinion-col\">{{ item.opinion }}</div>\n                  <div class=\"table-cell suggestion-col\">{{ item.suggestion }}\n                  </div>\n                  <div class=\"table-cell reading-col\">{{ item.reading }}</div>\n                  <div class=\"table-cell training-col\">{{ item.training }}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div class=\"right-panel\">\n        <!-- 社情民意 -->\n        <div class=\"social\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\" @click=\"handlePublicOpinionClick\">社情民意</span>\n            <span class=\"header_text_right\">本年</span>\n          </div>\n          <div class=\"social_content\">\n            <div class=\"social-data-container\">\n              <div class=\"left-data-item\">\n                <div class=\"left-data-label\">委员报送</div>\n                <div class=\"left-data-value\">总数<span>{{ socialData.memberSubmit.count }}</span>篇</div>\n                <div class=\"left-data-detail\">采用<span>{{ socialData.memberSubmit.adopted }}</span> 篇</div>\n              </div>\n              <div class=\"center-chart\">\n                <div class=\"progress-content\">\n                  <div class=\"total-number\">{{ socialData.total }}</div>\n                  <div class=\"total-label\">总数</div>\n                </div>\n              </div>\n              <div class=\"right-data-item\">\n                <div class=\"right-data-label\">单位报送</div>\n                <div class=\"right-data-value\">总数<span>{{ socialData.unitSubmit.count }}</span>篇</div>\n                <div class=\"right-data-detail\">采用<span>{{ socialData.unitSubmit.adopted }}</span> 篇</div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <!-- 会议活动 -->\n        <div class=\"conference_activities\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\">会议活动</span>\n            <span class=\"header_text_right\">本年</span>\n          </div>\n          <div class=\"conference_activities_content\">\n            <div class=\"activities-grid\">\n              <div v-for=\"(item, index) in conferenceActivitiesData\" :key=\"index\" class=\"activity-item\"\n                :class=\"getItemClass(item.name)\">\n                <div class=\"activity-value\">{{ item.value }}</div>\n                <div class=\"activity-name\">{{ item.name }}</div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <!-- 网络议政 -->\n        <div class=\"discussions\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\" @click=\"handleNetWorkClick\">网络议政</span>\n            <span class=\"header_text_right\"></span>\n          </div>\n          <div class=\"discussions_content\">\n            <!-- 统计数据区域 -->\n            <div class=\"statistics-section\">\n              <div v-for=\"(item, index) in discussionsData.statistics\" :key=\"index\" class=\"stat-item\">\n                <div class=\"stat-dot\"></div>\n                <div class=\"stat-info\">\n                  <span class=\"stat-name\">{{ item.name }}</span>\n                  <span class=\"stat-value\">{{ item.value }}</span>\n                  <span class=\"stat-unit\">{{ item.unit }}</span>\n                </div>\n              </div>\n            </div>\n\n            <!-- 最热话题区域 -->\n            <div class=\"hot-topics-section\">\n              <div class=\"hot-topics-header\">\n                <img src=\"../../../assets/largeScreen/icon_hot.png\" alt=\"热门\" class=\"hot-icon\">\n                <span class=\"hot-title\">最热话题</span>\n              </div>\n              <div class=\"topics-list\">\n                <div v-for=\"(topic, index) in discussionsData.hotTopics\" :key=\"index\" class=\"topic-item\">\n                  <div class=\"topic-dot\"></div>\n                  <span class=\"topic-text\">{{ topic }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { useIndex } from '../screen.js'\nimport MapComponent from '../components/MapComponent.vue'\nimport PieChart from '../components/PieChart.vue'\nimport BarScrollChart from '../components/BarScrollChart.vue'\n\nexport default {\n  name: 'BigScreen',\n  components: {\n    MapComponent,\n    PieChart,\n    BarScrollChart\n  },\n  data () {\n    return {\n      currentTime: '',\n      // 委员统计\n      committeeStatisticsNum: [\n        { icon: require('../../../assets/largeScreen/icon_cppcc_member.png'), label: '政协委员（人）', value: '10095', color: '#ffffff' },\n        { icon: require('../../../assets/largeScreen/icon_committee_member.png'), label: '政协常委', value: '8742', color: '#FCD603' }\n      ],\n      // 委员统计柱状图数据\n      committeeBarData: [\n        { name: '中共', value: 32 },\n        { name: '民革', value: 15 },\n        { name: '民盟', value: 14 },\n        { name: '民建', value: 13 },\n        { name: '民进', value: 12 },\n        { name: '农工', value: 10 },\n        { name: '致公', value: 8 },\n        { name: '九三', value: 7 },\n        { name: '台盟', value: 6 },\n        { name: '无党派', value: 5 }\n      ],\n      // 提案统计\n      proposalStatisticsNum: [\n        { icon: require('../../../assets/largeScreen/icon_committee_proposal.png'), label: '委员提案', value: '456', unit: '件', color: '#0DBCDB' },\n        { icon: require('../../../assets/largeScreen/icon_circles_proposal.png'), label: '界别提案', value: '354', unit: '件', color: '#0058FF' },\n        { icon: require('../../../assets/largeScreen/icon_committee_proposal.png'), label: '组织提案', value: '211', unit: '件', color: '#1AEBDD' }\n      ],\n      // 提案统计图表数据\n      proposalChartData: [\n        { name: '政府制约', value: 22.52 },\n        { name: '县区市政', value: 18.33 },\n        { name: '司法法治', value: 15.73 },\n        { name: '区市政府', value: 11.34 },\n        { name: '科技工商', value: 9.56 },\n        { name: '教育文化', value: 8.09 },\n        { name: '派出机构', value: 4.21 },\n        { name: '社会事业', value: 3.71 },\n        { name: '企事业', value: 3.65 },\n        { name: '农村卫生', value: 3.21 },\n        { name: '其他机构', value: 1.86 },\n        { name: '各群体他', value: 1.02 }\n      ],\n      proposalChartName: '提案统计',\n      // 工作动态数据\n      workDynamicsData: [\n        {\n          id: 1,\n          title: '市政协社会和法制工作办公室围绕\"居家适老化改造\"开展专题调研',\n          date: '2025-06-03'\n        },\n        {\n          id: 2,\n          title: '\"与民同行 共创共赢\"新格局下民营企业转型发展座谈会召开',\n          date: '2025-05-30'\n        },\n        {\n          id: 3,\n          title: '\"惠民生·基层行\"义诊活动温暖人心',\n          date: '2025-05-30'\n        },\n        {\n          id: 4,\n          title: '市科技局面复市政协科技界别提案',\n          date: '2025-05-30'\n        },\n        {\n          id: 5,\n          title: '市政协召开\"推进数字化转型\"专题协商会',\n          date: '2025-05-28'\n        },\n        {\n          id: 6,\n          title: '政协委员深入基层开展\"三服务\"活动',\n          date: '2025-05-25'\n        }\n      ],\n      // 履职统计数据\n      performanceData: [\n        { name: '马平安', meeting: 515, proposal: 15, opinion: 0, suggestion: 0, reading: 0, training: 0 },\n        { name: '马波', meeting: 400, proposal: 0, opinion: 0, suggestion: 12, reading: 0, training: 15 },\n        { name: '王玉民', meeting: 490, proposal: 1, opinion: 2, suggestion: 0, reading: 4, training: 25 },\n        { name: '王俊宝', meeting: 500, proposal: 0, opinion: 4, suggestion: 1, reading: 5, training: 60 },\n        { name: '李明', meeting: 320, proposal: 8, opinion: 1, suggestion: 3, reading: 2, training: 18 },\n        { name: '张华', meeting: 280, proposal: 5, opinion: 0, suggestion: 2, reading: 1, training: 12 },\n        { name: '刘强', meeting: 450, proposal: 3, opinion: 6, suggestion: 0, reading: 3, training: 35 },\n        { name: '陈静', meeting: 380, proposal: 2, opinion: 3, suggestion: 4, reading: 6, training: 28 }\n      ],\n      // 社情民意数据\n      socialData: {\n        memberSubmit: {\n          count: 345,\n          adopted: 21\n        },\n        unitSubmit: {\n          count: 547,\n          adopted: 79\n        },\n        total: 1057\n      },\n      // 会议活动数据\n      conferenceActivitiesData: [\n        { name: '会议次数', value: 201 },\n        { name: '活动次数', value: 310 },\n        { name: '会议人数', value: 2412 },\n        { name: '活动人数', value: 4015 }\n      ],\n      // 网络议政数据\n      discussionsData: {\n        statistics: [\n          { name: '发布议题', value: 72, unit: '个' },\n          { name: '累计参与人次', value: 39301, unit: '次' },\n          { name: '累计征求意见', value: 12306, unit: '条' }\n        ],\n        hotTopics: [\n          '推进黄河国家文化公园建设',\n          '持续推进黄河流域生态保护修复，助力\"先行区\"建设',\n          '全面加强新时代中小学劳动教育'\n        ]\n      },\n      mapData: [\n        { name: '市南区', value: '1200', areaId: '370202', adcode: 370202 },\n        { name: '市北区', value: '1300', areaId: '370203', adcode: 370203 },\n        { name: '黄岛区', value: '850', areaId: '370211', adcode: 370211 },\n        { name: '崂山区', value: '700', areaId: '370212', adcode: 370212 },\n        { name: '李沧区', value: '1000', areaId: '370213', adcode: 370213 },\n        { name: '城阳区', value: '1100', areaId: '370214', adcode: 370214 },\n        { name: '即墨区', value: '950', areaId: '370215', adcode: 370215 },\n        { name: '胶州市', value: '800', areaId: '370281', adcode: 370281 },\n        { name: '平度市', value: '1400', areaId: '370283', adcode: 370283 },\n        { name: '莱西市', value: '600', areaId: '370285', adcode: 370285 }\n      ],\n      areaId: JSON.parse(sessionStorage.getItem('areaId' + this.$logo())),\n      areaName: '青岛市'\n    }\n  },\n  computed: {\n  },\n  mounted () {\n    this.initScreen()\n    this.updateTime()\n    this.timeInterval = setInterval(this.updateTime, 1000)\n  },\n  beforeDestroy () {\n    if (this.timeInterval) {\n      clearInterval(this.timeInterval)\n    }\n  },\n  methods: {\n    initScreen () {\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\n      calcRate()\n      windowDraw()\n    },\n    updateTime () {\n      const now = new Date()\n      this.currentTime = now.toLocaleString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit',\n        second: '2-digit'\n      })\n    },\n    getItemClass (name) {\n      if (name.includes('会议')) {\n        return 'meeting-item'\n      } else if (name.includes('活动')) {\n        return 'activity-item-bg'\n      }\n      return ''\n    },\n    handleRegionClick (region) {\n      console.log('选中地区:', region)\n      // 这里可以添加地区点击后的业务逻辑\n      // 比如显示该地区的详细数据等\n    },\n    // 打开委员统计\n    handleCommitteeClick () {\n      this.$router.push({ path: '/committeeStatisticsBox', query: { route: '/committeeStatisticsBox' } })\n    },\n    // 打开提案统计\n    handleProposalClick () {\n      this.$router.push({ path: '/proposalStatisticsBox', query: { route: '/proposalStatisticsBox' } })\n    },\n    // 打开履职统计\n    handlePerformanceClick () {\n      this.$router.push({ path: '/performanceStatisticsBox', query: { route: '/performanceStatisticsBox' } })\n    },\n    // 打开网络议政\n    handleNetWorkClick () {\n      this.$router.push({ path: '/networkDiscussBox', query: { route: '/networkDiscussBox' } })\n    },\n    // 打开社情民意\n    handlePublicOpinionClick () {\n      this.$router.push({ path: '/publicOpinionBox', query: { route: '/publicOpinionBox' } })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.big-screen {\n  width: 1920px;\n  height: 1080px;\n  position: relative;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  transform-origin: left top;\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\n  background-size: cover;\n  background-position: center;\n\n  .screen-header {\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\n    background-size: 100% 100%;\n    background-position: center;\n    height: 65px;\n    display: flex;\n    align-items: center;\n    padding: 0 40px;\n\n    .header-left {\n      display: flex;\n      gap: 20px;\n      font-size: 14px;\n      color: #8cc8ff;\n      flex: 1;\n    }\n\n    .header-center {\n      width: 60%;\n      text-align: center;\n    }\n\n    .header-right {\n      flex: 1;\n    }\n  }\n\n  .screen-content {\n    height: calc(100% - 65px);\n    display: flex;\n    padding: 35px 20px 0 20px;\n    gap: 30px;\n\n    .header_box {\n      position: absolute;\n      top: 15px;\n      left: 24px;\n      right: 0;\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n\n      .header_text_left {\n        font-weight: bold;\n        font-size: 20px;\n        color: #FFFFFF;\n        cursor: pointer;\n      }\n\n      .header_text_right {\n        font-size: 15px;\n        color: #FFD600;\n      }\n\n      .header_text_center {\n        font-size: 15px;\n        color: #FFFFFF;\n        display: flex;\n        align-items: center;\n\n        span {\n          font-weight: 500;\n          font-size: 24px;\n          color: #02FBFB;\n          margin: 0 10px 0 6px;\n        }\n      }\n    }\n\n    .left-panel,\n    .right-panel {\n      width: 470px;\n      display: flex;\n      flex-direction: column;\n      gap: 20px 30px;\n    }\n\n    .left-panel {\n      .committee_statistics {\n        background: url('../../../assets/largeScreen/committee_statistics_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 320px;\n        width: 100%;\n\n        .committee_statistics_content {\n          height: 100%;\n          margin-top: 72px;\n          margin-left: 20px;\n          margin-right: 20px;\n\n          .committee_statistics_num {\n            display: flex;\n            align-items: center;\n            justify-content: space-around;\n\n            .num_box {\n              display: flex;\n              align-items: center;\n\n              .num_icon {\n                width: 64px;\n                height: 64px;\n                margin-right: 14px;\n              }\n\n              .num_label {\n                font-size: 15px;\n                color: #B4C0CC;\n                margin-bottom: 14px;\n              }\n\n              .num_value {\n                font-weight: bold;\n                font-size: 26px;\n                color: #FFFFFF;\n              }\n            }\n          }\n\n          .committee_statistics_chart {\n            width: 100%;\n            height: 180px;\n          }\n        }\n      }\n\n      .proposal_statistics {\n        background: url('../../../assets/largeScreen/proposal_statistics_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 310px;\n        width: 100%;\n\n        .proposal_statistics_content {\n          height: 100%;\n          margin-top: 72px;\n          margin-left: 20px;\n          margin-right: 20px;\n\n          .proposal_statistics_num {\n            display: flex;\n            align-items: center;\n            justify-content: space-between;\n\n            .num_box {\n              display: flex;\n              align-items: center;\n\n              .num_icon {\n                width: 54px;\n                height: 54px;\n                margin-right: 10px;\n              }\n\n              .num_label {\n                font-size: 14px;\n                color: #FFFFFF;\n                margin-bottom: 5px;\n              }\n\n              .num_value {\n                font-size: 20px;\n                color: #0DBCDB;\n                font-weight: 500;\n\n                .num_unit {\n                  font-size: 14px;\n                  color: #FFFFFF;\n                  font-weight: normal;\n                  margin-left: 4px;\n                }\n              }\n            }\n          }\n\n          .proposal_statistics_chart {\n            height: 150px;\n            margin-top: 20px;\n          }\n        }\n      }\n\n      .work_dynamics {\n        background: url('../../../assets/largeScreen/work_dynamics_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 270px;\n        width: 100%;\n\n        .work_dynamics_content {\n          height: 100%;\n          margin-top: 65px;\n          margin-left: 14px;\n          margin-right: 14px;\n\n          .dynamics-list {\n            height: calc(100% - 70px);\n            overflow-y: auto;\n\n            &::-webkit-scrollbar {\n              width: 4px;\n            }\n\n            &::-webkit-scrollbar-track {\n              background: rgba(0, 30, 60, 0.3);\n              border-radius: 2px;\n            }\n\n            &::-webkit-scrollbar-thumb {\n              background: rgba(0, 212, 255, 0.4);\n              border-radius: 2px;\n\n              &:hover {\n                background: rgba(0, 212, 255, 0.6);\n              }\n            }\n\n            .dynamics-item {\n              margin-bottom: 12px;\n              overflow: hidden;\n              position: relative;\n\n              &:last-child {\n                margin-bottom: 0;\n              }\n\n              // 奇数项 - 背景图片样式\n              &.with-bg-image {\n                background: url('../../../assets/largeScreen/table_bg.png') no-repeat;\n                background-size: 100% 100%;\n                background-position: center;\n              }\n\n              // 偶数项 - 背景颜色样式\n              &.with-bg-color {\n                background: rgba(6, 79, 219, 0.05);\n              }\n\n              .dynamics-content {\n                padding: 12px 15px;\n                position: relative;\n                z-index: 2;\n                display: flex;\n                justify-content: space-between;\n                align-items: center;\n\n                .dynamics-title {\n                  flex: 1;\n                  color: #fff;\n                  font-size: 16px;\n                  margin-right: 16px;\n                  // 文本溢出处理\n                  display: -webkit-box;\n                  -webkit-line-clamp: 1;\n                  -webkit-box-orient: vertical;\n                  overflow: hidden;\n                  text-overflow: ellipsis;\n                }\n\n                .dynamics-date {\n                  flex-shrink: 0;\n                  font-size: 16px;\n                  color: #FFFFFF;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    .right-panel {\n      .social {\n        background: url('../../../assets/largeScreen/social_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 290px;\n        width: 100%;\n\n        .social_content {\n          height: 190px;\n          margin-top: 75px;\n          margin-left: 12px;\n          margin-right: 12px;\n          background: url('../../../assets/largeScreen/social_content_bg.png') no-repeat;\n          background-size: 100% 100%;\n          background-position: center;\n\n          .social-data-container {\n            display: flex;\n            align-items: center;\n            justify-content: space-between;\n            width: 100%;\n            height: 100%;\n\n            .left-data-item {\n              display: flex;\n              flex-direction: column;\n              align-items: center;\n              text-align: center;\n              flex: 1;\n              margin-right: 20px;\n\n              .left-data-label {\n                font-size: 14px;\n                color: #19ECFF;\n                margin-bottom: 20px;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n              }\n\n              .left-data-value {\n                font-size: 14px;\n                color: #FFFFFF;\n                margin-bottom: 15px;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n\n                span {\n                  font-weight: 400;\n                  font-size: 20px;\n                  color: #FFD600;\n                  margin: 0 5px;\n                }\n              }\n\n              .left-data-detail {\n                font-size: 14px;\n                color: #FFFFFF;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n\n                span {\n                  font-weight: 400;\n                  font-size: 20px;\n                  color: #19ECFF;\n                  margin: 0 5px;\n                }\n              }\n            }\n\n            .center-chart {\n              flex: 1;\n              display: flex;\n              justify-content: center;\n              align-items: center;\n\n              .progress-content {\n                position: absolute;\n                display: flex;\n                flex-direction: column;\n                align-items: center;\n                justify-content: center;\n\n                .total-number {\n                  font-weight: 500;\n                  font-size: 24px;\n                  color: #FFD600;\n                  margin-bottom: 8px;\n                }\n\n                .total-label {\n                  font-size: 14px;\n                  color: #ffffff;\n                }\n              }\n            }\n\n            .right-data-item {\n              display: flex;\n              flex-direction: column;\n              align-items: center;\n              text-align: center;\n              flex: 1;\n              margin-left: 20px;\n\n              .right-data-label {\n                font-size: 14px;\n                color: #19ECFF;\n                margin-bottom: 20px;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n              }\n\n              .right-data-value {\n                font-size: 14px;\n                color: #FFFFFF;\n                margin-bottom: 15px;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n\n                span {\n                  font-weight: 400;\n                  font-size: 20px;\n                  color: #FFD600;\n                  margin: 0 5px;\n                }\n              }\n\n              .right-data-detail {\n                font-size: 14px;\n                color: #FFFFFF;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n\n                span {\n                  font-weight: 400;\n                  font-size: 20px;\n                  color: #19ECFF;\n                  margin: 0 5px;\n                }\n              }\n            }\n          }\n        }\n      }\n\n      .conference_activities {\n        background: url('../../../assets/largeScreen/conference_activities_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 290px;\n        width: 100%;\n\n        .conference_activities_content {\n          margin-top: 70px;\n          margin-left: 20px;\n          margin-right: 20px;\n\n          .activities-grid {\n            display: grid;\n            grid-template-columns: 1fr 1fr;\n            grid-template-rows: 1fr 1fr;\n            gap: 15px;\n            height: 100%;\n\n            .activity-item {\n              display: flex;\n              flex-direction: column;\n              align-items: center;\n              justify-content: center;\n              background-size: 100% 100%;\n              background-repeat: no-repeat;\n              background-position: center;\n              height: 92px;\n\n              .activity-value {\n                font-weight: 500;\n                font-size: 32px;\n                color: #FFFFFF;\n                line-height: 24px;\n                margin-bottom: 12px;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n              }\n\n              .activity-name {\n                font-size: 16px;\n                color: #FFFFFF;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n              }\n\n              &.meeting-item {\n                background-image: url('../../../assets/largeScreen/icon_meeting_item_bg.png');\n              }\n\n              &.activity-item-bg {\n                background-image: url('../../../assets/largeScreen/icon_activity_item_bg.png');\n              }\n            }\n          }\n        }\n      }\n\n      .discussions {\n        background: url('../../../assets/largeScreen/discussions_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 320px;\n        width: 100%;\n\n        .discussions_content {\n          margin-top: 75px;\n          margin-left: 20px;\n          margin-right: 20px;\n          height: calc(100% - 90px);\n          display: flex;\n          flex-direction: column;\n\n          .statistics-section {\n            display: flex;\n            flex-wrap: wrap;\n            justify-content: space-between;\n            gap: 20px;\n            margin-bottom: 25px;\n\n            .stat-item {\n              display: flex;\n              align-items: center;\n              gap: 12px;\n\n              .stat-dot {\n                width: 10px;\n                height: 10px;\n                border-radius: 50%;\n                background: linear-gradient(180deg, #00EEFF 0%, #E1FDFF 100%);\n                flex-shrink: 0;\n                margin-top: 5px;\n              }\n\n              .stat-info {\n                display: flex;\n                align-items: center;\n                gap: 8px;\n\n                .stat-name {\n                  font-size: 15px;\n                  color: #FFFFFF;\n                  font-family: DIN-BoldItalic, DIN-BoldItalic;\n                }\n\n                .stat-value {\n                  font-weight: 500;\n                  font-size: 20px;\n                  color: #FFD600;\n                  font-family: DIN-BoldItalic, DIN-BoldItalic;\n                }\n\n                .stat-unit {\n                  font-size: 15px;\n                  color: #FFFFFF;\n                  font-family: DIN-BoldItalic, DIN-BoldItalic;\n                }\n              }\n            }\n          }\n\n          .hot-topics-section {\n            flex: 1;\n            background: rgba(31, 198, 255, 0.16);\n            padding: 12px 16px;\n\n            .hot-topics-header {\n              display: flex;\n              align-items: center;\n              gap: 8px;\n              margin-bottom: 15px;\n\n              .hot-icon {\n                width: 20px;\n                height: 20px;\n              }\n\n              .hot-title {\n                font-size: 16px;\n                color: #02FBFB;\n                font-weight: 500;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n              }\n            }\n\n            .topics-list {\n              display: flex;\n              flex-direction: column;\n              gap: 12px;\n\n              .topic-item {\n                display: flex;\n                align-items: flex-start;\n                gap: 10px;\n\n                .topic-dot {\n                  width: 6px;\n                  height: 6px;\n                  border-radius: 50%;\n                  background: rgba(217, 217, 217, 0.5);\n                  margin-top: 8px;\n                  flex-shrink: 0;\n                }\n\n                .topic-text {\n                  font-size: 14px;\n                  color: #FFFFFF;\n                  line-height: 22px;\n                  font-family: DIN-BoldItalic, DIN-BoldItalic;\n                  text-overflow: ellipsis;\n                  overflow: hidden;\n                  white-space: nowrap;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    .center-panel {\n      flex: 1;\n      gap: 20px;\n      display: flex;\n      flex-direction: column;\n\n      .map_box {\n        background: url('../../../assets/largeScreen/map_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        height: 650px;\n        width: 100%;\n        position: relative;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n\n      .performance_statistics {\n        background: url('../../../assets/largeScreen/performance_statistics_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 270px;\n        width: 100%;\n\n        .performance_statistics_content {\n          height: 100%;\n          margin-top: 65px;\n          margin-left: 16px;\n          margin-right: 16px;\n\n          .table-container {\n            height: calc(100% - 75px);\n            display: flex;\n            flex-direction: column;\n            border: 1px solid rgba(0, 212, 255, 0.2);\n            overflow: hidden;\n            /* 使用CSS Grid确保列对齐 */\n            --name-col-width: 120px;\n            --scrollbar-width: 6px;\n\n            .table-header {\n              display: grid;\n              grid-template-columns: var(--name-col-width) repeat(6, 1fr) var(--scrollbar-width);\n              border-bottom: 1px solid rgba(0, 212, 255, 0.4);\n              position: sticky;\n              top: 0;\n              z-index: 10;\n\n              .header-cell {\n                padding: 12px 8px;\n                text-align: center;\n                color: #E6F7FF;\n                font-size: 15px;\n                border-right: 1px solid rgba(0, 212, 255, 0.3);\n                display: flex;\n                align-items: center;\n                justify-content: center;\n\n                &:last-child {\n                  border-right: none;\n                  background: transparent;\n                  border: none;\n                }\n\n                // &.name-col {\n                //   background: rgba(0, 100, 180, 0.9);\n                //   font-weight: 600;\n                // }\n              }\n            }\n\n            .table-body {\n              flex: 1;\n              overflow-y: auto;\n\n              &::-webkit-scrollbar {\n                width: 6px;\n              }\n\n              &::-webkit-scrollbar-track {\n                background: rgba(0, 30, 60, 0.3);\n                border-radius: 3px;\n              }\n\n              &::-webkit-scrollbar-thumb {\n                background: rgba(0, 212, 255, 0.4);\n                border-radius: 3px;\n\n                &:hover {\n                  background: rgba(0, 212, 255, 0.6);\n                }\n              }\n\n              .table-row {\n                display: grid;\n                grid-template-columns: var(--name-col-width) repeat(6, 1fr);\n                border-bottom: 1px solid rgba(0, 212, 255, 0.4);\n                transition: all 0.3s ease;\n\n                &:hover {\n                  background: rgba(0, 212, 255, 0.1);\n                }\n\n                .table-cell {\n                  padding: 12px 8px;\n                  text-align: center;\n                  color: #FFFFFF;\n                  font-size: 14px;\n                  border-right: 1px solid rgba(0, 212, 255, 0.4);\n                  transition: all 0.3s ease;\n                  display: flex;\n                  align-items: center;\n                  justify-content: center;\n\n                  &:last-child {\n                    border-right: none;\n                  }\n\n                  &.name-col {\n                    background: rgba(0, 60, 120, 0.4);\n                    color: #FFF;\n                    font-weight: 500;\n                  }\n\n                  &.meeting-col {\n                    background: rgba(10, 63, 111, 0.4);\n                    color: #59F7CA;\n                    font-weight: 500;\n                    font-size: 16px;\n                  }\n\n                  &.proposal-col {\n                    background: rgba(10, 63, 111, 0.4);\n                    font-weight: 500;\n                    font-size: 16px;\n                    color: #00FFF7;\n                  }\n\n                  &.opinion-col {\n                    background: rgba(10, 63, 111, 0.4);\n                    font-weight: 500;\n                    font-size: 16px;\n                    color: #FF386B;\n                  }\n\n                  &.suggestion-col {\n                    background: rgba(10, 63, 111, 0.4);\n                    font-weight: 500;\n                    font-size: 16px;\n                    color: #81C4E4;\n                  }\n\n                  &.reading-col {\n                    background: rgba(10, 63, 111, 0.4);\n                    font-weight: 500;\n                    font-size: 16px;\n                    color: #387BFD;\n                  }\n\n                  &.training-col {\n                    background: rgba(10, 63, 111, 0.4);\n                    font-weight: 500;\n                    font-size: 16px;\n                    color: #FF911F;\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}