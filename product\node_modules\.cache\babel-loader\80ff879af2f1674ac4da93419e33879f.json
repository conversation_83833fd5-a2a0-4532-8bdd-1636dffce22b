{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\publicOpinion\\publicOpinionBox.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\publicOpinion\\publicOpinionBox.vue", "mtime": 1756457015308}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAsHA;AACA;AACA;AACA;EACAA,iBADA;EAEAC;IACAC,SADA;IAEAC;EAFA,CAFA;;EAMAC;IACA;MACAC,eADA;MAEAC,eACA;QAAAN;QAAAO;MAAA,CADA,EAEA;QAAAP;QAAAO;MAAA,CAFA,EAGA;QAAAP;QAAAO;MAAA,CAHA,EAIA;QAAAP;QAAAO;MAAA,CAJA,EAKA;QAAAP;QAAAO;MAAA,CALA,EAMA;QAAAP;QAAAO;MAAA,CANA,EAOA;QAAAP;QAAAO;MAAA,CAPA,EAQA;QAAAP;QAAAO;MAAA,CARA,EASA;QAAAP;QAAAO;MAAA,CATA,EAUA;QAAAP;QAAAO;MAAA,CAVA,EAWA;QAAAP;QAAAO;MAAA,CAXA,EAYA;QAAAP;QAAAO;MAAA,CAZA,EAaA;QAAAP;QAAAO;MAAA,CAbA,EAcA;QAAAP;QAAAO;MAAA,CAdA,EAeA;QAAAP;QAAAO;MAAA,CAfA,CAFA;MAmBA;MACAC,sBACA;QAAAR;QAAAO;QAAAE;MAAA,CADA,EAEA;QAAAT;QAAAO;QAAAE;MAAA,CAFA,EAGA;QAAAT;QAAAO;QAAAE;MAAA,CAHA,EAIA;QAAAT;QAAAO;QAAAE;MAAA,CAJA,EAKA;QAAAT;QAAAO;QAAAE;MAAA,CALA,CApBA;MA2BAC,2BA3BA;MA4BA;MACAC,uBACA;QAAAX;QAAAY;QAAAC;QAAAC;MAAA,CADA,EAEA;QAAAd;QAAAY;QAAAC;QAAAC;MAAA,CAFA,EAGA;QAAAd;QAAAY;QAAAC;QAAAC;MAAA,CAHA,EAIA;QAAAd;QAAAY;QAAAC;QAAAC;MAAA,CAJA,EAKA;QAAAd;QAAAY;QAAAC;QAAAC;MAAA,CALA,EAMA;QAAAd;QAAAY;QAAAC;QAAAC;MAAA,CANA,EAOA;QAAAd;QAAAY;QAAAC;QAAAC;MAAA,CAPA,EAQA;QAAAd;QAAAY;QAAAC;QAAAC;MAAA,CARA;IA7BA;EAwCA,CA/CA;;EAgDAC,YAhDA;;EAkDAC;IACA;IACA;IACA;EACA,CAtDA;;EAuDAC;IACA;MACAC;IACA;EACA,CA3DA;;EA4DAC;IACAC;MACA;QAAAC;QAAAC;MAAA;MACAD;MACAC;IACA,CALA;;IAMAC;MACA;MACA;QACAC,eADA;QAEAC,gBAFA;QAGAC,cAHA;QAIAC,eAJA;QAKAC,iBALA;QAMAC;MANA;IAQA,CAhBA;;IAiBA;IACAC;MACA;QAAAC;MAAA;IACA,CApBA;;IAqBA;IACAC;MACAC;IACA;;EAxBA;AA5DA", "names": ["name", "components", "WordCloud", "<PERSON><PERSON><PERSON>", "data", "currentTime", "hotWordsData", "value", "socialTypeChartData", "percentage", "socialTypeChartName", "personalAdoptionData", "avatar", "submitCount", "adoptCount", "computed", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "initScreen", "calcRate", "windowDraw", "updateTime", "year", "month", "day", "hour", "minute", "second", "goHome", "path", "onWordClick", "console"], "sourceRoot": "src/views/smartBrainLargeScreen/publicOpinion", "sources": ["publicOpinionBox.vue"], "sourcesContent": ["<template>\r\n  <div class=\"big-screen\" ref=\"bigScreen\">\r\n    <div class=\"screen-header\">\r\n      <div class=\"header-left\">\r\n        <span class=\"date-time\">{{ currentTime }}</span>\r\n        <span class=\"weather\">晴 24℃ 东南风</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <div class=\"header-buttons\">\r\n          <div class=\"header-btn current-module-btn\">\r\n            <span>社情民意</span>\r\n          </div>\r\n          <div class=\"header-btn home-btn\" @click=\"goHome\">\r\n            <span>返回首页</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"screen-content\">\r\n      <div class=\"left-panel\">\r\n        <!-- 采用情况 -->\r\n        <div class=\"social_adoption_scenario\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">采用情况</span>\r\n          </div>\r\n          <div class=\"social_adoption_scenario_content\">\r\n          </div>\r\n        </div>\r\n        <!-- 类型分析 -->\r\n        <div class=\"social_type_analysis\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">类型分析</span>\r\n          </div>\r\n          <div class=\"social_type_analysis_content\">\r\n            <PieChart id=\"social_type\" :chart-data=\"socialTypeChartData\" :name=\"socialTypeChartName\"\r\n              :legendItemWidth=\"13\" :legendItemHeight=\"13\" :radius=\"['48%', '70%']\" :center=\"['30%', '50%']\"\r\n              :lineRadius=\"['78%', '79%']\" :lineCenter=\"['30%', '50%']\" graphicLeft=\"18.2%\" graphicTop=\"28%\"\r\n              :graphicShapeR=\"55\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"center-panel\">\r\n        <!-- 社情民意整体情况 -->\r\n        <div class=\"social_overall_situation\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">社情民意整体情况</span>\r\n          </div>\r\n          <div class=\"social_overall_situation_content\">\r\n          </div>\r\n        </div>\r\n        <!-- 各单位报送情况 -->\r\n        <div class=\"each_unit_submits\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">各单位报送情况</span>\r\n          </div>\r\n          <div class=\"each_unit_submits_content\">\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"right-panel\">\r\n        <!-- 批示情况 -->\r\n        <div class=\"approval_status\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">批示情况</span>\r\n          </div>\r\n          <div class=\"approval_status_content\">\r\n          </div>\r\n        </div>\r\n        <!-- 个人报送和采用情况 -->\r\n        <div class=\"personal_adoption\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">个人报送和采用情况</span>\r\n          </div>\r\n          <div class=\"personal_adoption_content\">\r\n            <div class=\"personal-table-container\">\r\n              <div class=\"table-header\">\r\n                <div class=\"header-cell name-cell\">姓名</div>\r\n                <div class=\"header-cell submit-cell\">报送数量</div>\r\n                <div class=\"header-cell adopt-cell\">采用数量</div>\r\n              </div>\r\n              <div class=\"table-body\">\r\n                <div v-for=\"(item, index) in personalAdoptionData\" :key=\"index\" class=\"table-row\">\r\n                  <div class=\"body-cell name-cell\">\r\n                    <div class=\"avatar-container\">\r\n                      <img :src=\"item.avatar\" :alt=\"item.name\" class=\"avatar\">\r\n                      <span class=\"name\">{{ item.name }}</span>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"body-cell submit-cell\">\r\n                    <span class=\"submit-count\">{{ item.submitCount }}</span>\r\n                  </div>\r\n                  <div class=\"body-cell adopt-cell\">\r\n                    <span class=\"adopt-count\">{{ item.adoptCount }}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 热词分析 -->\r\n        <div class=\"hot_word_analysis\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">热词分析</span>\r\n            <span class=\"header_text_right\"></span>\r\n          </div>\r\n          <div class=\"hot_word_analysis_content\">\r\n            <WordCloud chart-id=\"hotWordChart\" :words=\"hotWordsData\" @word-click=\"onWordClick\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { useIndex } from '../screen.js'\r\nimport WordCloud from '../components/WordCloud.vue'\r\nimport PieChart from '../components/PieChart.vue'\r\nexport default {\r\n  name: 'BigScreen',\r\n  components: {\r\n    WordCloud,\r\n    PieChart\r\n  },\r\n  data () {\r\n    return {\r\n      currentTime: '',\r\n      hotWordsData: [\r\n        { name: '经济建设', value: 10 },\r\n        { name: '人才培养', value: 9 },\r\n        { name: 'AI技术', value: 6 },\r\n        { name: '改革创新', value: 7 },\r\n        { name: '教育', value: 5 },\r\n        { name: '车辆交通', value: 6 },\r\n        { name: '旅游', value: 5 },\r\n        { name: '公共安全', value: 7 },\r\n        { name: '智能化', value: 6 },\r\n        { name: '电梯故障', value: 4 },\r\n        { name: '社会保障', value: 6 },\r\n        { name: '环境保护', value: 5 },\r\n        { name: '医疗卫生', value: 7 },\r\n        { name: '文化建设', value: 4 },\r\n        { name: '科技创新', value: 8 }\r\n      ],\r\n      // 类型分析\r\n      socialTypeChartData: [\r\n        { name: '社会', value: 20, percentage: '5%' },\r\n        { name: '政治', value: 125, percentage: '30%' },\r\n        { name: '经济', value: 168, percentage: '40%' },\r\n        { name: '文化', value: 85, percentage: '20%' },\r\n        { name: '生态文明', value: 20, percentage: '5%' }\r\n      ],\r\n      socialTypeChartName: '类别分析',\r\n      // 个人报送和采用情况数据\r\n      personalAdoptionData: [\r\n        { name: '马波', avatar: require('@/assets/images/man-head.png'), submitCount: 12, adoptCount: 3 },\r\n        { name: '王玉明', avatar: require('@/assets/images/woman-head.png'), submitCount: 11, adoptCount: 2 },\r\n        { name: '王忠', avatar: require('@/assets/images/man-head.png'), submitCount: 11, adoptCount: 2 },\r\n        { name: '汪杨波', avatar: require('@/assets/images/man-head.png'), submitCount: 10, adoptCount: 2 },\r\n        { name: '黄毅', avatar: require('@/assets/images/man-head.png'), submitCount: 8, adoptCount: 1 },\r\n        { name: '李明', avatar: require('@/assets/images/man-head.png'), submitCount: 7, adoptCount: 1 },\r\n        { name: '张三', avatar: require('@/assets/images/man-head.png'), submitCount: 6, adoptCount: 1 },\r\n        { name: '李四', avatar: require('@/assets/images/woman-head.png'), submitCount: 5, adoptCount: 0 }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n  },\r\n  mounted () {\r\n    this.initScreen()\r\n    this.updateTime()\r\n    this.timeInterval = setInterval(this.updateTime, 1000)\r\n  },\r\n  beforeDestroy () {\r\n    if (this.timeInterval) {\r\n      clearInterval(this.timeInterval)\r\n    }\r\n  },\r\n  methods: {\r\n    initScreen () {\r\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\r\n      calcRate()\r\n      windowDraw()\r\n    },\r\n    updateTime () {\r\n      const now = new Date()\r\n      this.currentTime = now.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      })\r\n    },\r\n    // 返回首页\r\n    goHome () {\r\n      this.$router.push({ path: '/homeBox' })\r\n    },\r\n    // 词云点击事件\r\n    onWordClick (word) {\r\n      console.log('词汇点击:', word)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.big-screen {\r\n  width: 1920px;\r\n  height: 1080px;\r\n  position: relative;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transform-origin: left top;\r\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\r\n  background-size: cover;\r\n  background-position: center;\r\n\r\n  .screen-header {\r\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\r\n    background-size: 100% 100%;\r\n    background-position: center;\r\n    height: 65px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 40px;\r\n\r\n    .header-left {\r\n      display: flex;\r\n      gap: 20px;\r\n      font-size: 14px;\r\n      color: #8cc8ff;\r\n      flex: 1;\r\n    }\r\n\r\n    .header-center {\r\n      width: 60%;\r\n      text-align: center;\r\n    }\r\n\r\n    .header-right {\r\n      flex: 1;\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      align-items: center;\r\n\r\n      .header-buttons {\r\n        display: flex;\r\n        gap: 15px;\r\n\r\n        .header-btn {\r\n          height: 42px;\r\n          line-height: 42px;\r\n          padding: 0 16px;\r\n          cursor: pointer;\r\n          transition: all 0.3s ease;\r\n          position: relative;\r\n          overflow: hidden;\r\n\r\n          &::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: -100%;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n            transition: left 0.5s;\r\n          }\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);\r\n            border-color: rgba(0, 181, 254, 1);\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);\r\n\r\n            &::before {\r\n              left: 100%;\r\n            }\r\n          }\r\n\r\n          &.current-module-btn {\r\n            background: url('../../../assets/largeScreen/icon_committee.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: bold;\r\n            font-size: 16px;\r\n            color: #FFFFFF;\r\n          }\r\n\r\n          &.home-btn {\r\n            background: url('../../../assets/largeScreen/icon_back_home.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: 400;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n          }\r\n\r\n          &.area-select-btn {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 0.2) 0%, rgba(0, 120, 220, 0.2) 100%);\r\n            border: 1px solid rgba(0, 181, 254, 0.5);\r\n            border-radius: 6px;\r\n            font-weight: 500;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            min-width: 120px;\r\n\r\n            .dropdown-icon {\r\n              margin-left: 8px;\r\n              font-size: 12px;\r\n              transition: transform 0.3s ease;\r\n              color: #1FC6FF;\r\n\r\n              &.active {\r\n                transform: rotate(180deg);\r\n              }\r\n            }\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(0, 181, 254, 0.3) 0%, rgba(0, 120, 220, 0.3) 100%);\r\n              border-color: rgba(0, 181, 254, 0.8);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .screen-content {\r\n    height: calc(100% - 65px);\r\n    display: flex;\r\n    padding: 35px 20px 0 20px;\r\n    gap: 30px;\r\n\r\n    .header_box {\r\n      position: absolute;\r\n      top: 15px;\r\n      left: 24px;\r\n      right: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n\r\n      .header_text_left {\r\n        font-weight: bold;\r\n        font-size: 20px;\r\n        color: #FFFFFF;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .header_text_right {\r\n        font-size: 15px;\r\n        color: #FFD600;\r\n      }\r\n\r\n      .header_text_center {\r\n        font-size: 15px;\r\n        color: #FFFFFF;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          font-weight: 500;\r\n          font-size: 24px;\r\n          color: #02FBFB;\r\n          margin: 0 10px 0 6px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .left-panel,\r\n    .right-panel {\r\n      width: 490px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 20px 30px;\r\n    }\r\n\r\n    .left-panel {\r\n      .social_adoption_scenario {\r\n        background: url('../../../assets/largeScreen/social_adoption_scenario_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 610px;\r\n        width: 100%;\r\n\r\n        .social_adoption_scenario_content {\r\n          height: 100%;\r\n          margin-top: 72px;\r\n          margin-left: 20px;\r\n          margin-right: 20px;\r\n        }\r\n      }\r\n\r\n      .social_type_analysis {\r\n        background: url('../../../assets/largeScreen/social_type_analysis_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 300px;\r\n        width: 100%;\r\n\r\n        .social_type_analysis_content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n    }\r\n\r\n    .right-panel {\r\n      .approval_status {\r\n        background: url('../../../assets/largeScreen/approval_status_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 230px;\r\n        width: 100%;\r\n\r\n        .approval_status_content {\r\n          margin-top: 75px;\r\n          margin-left: 12px;\r\n          margin-right: 12px;\r\n        }\r\n      }\r\n\r\n      .personal_adoption {\r\n        background: url('../../../assets/largeScreen/personal_adoption_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 350px;\r\n        width: 100%;\r\n\r\n        .personal_adoption_content {\r\n          margin-top: 70px;\r\n          margin-left: 20px;\r\n          margin-right: 20px;\r\n          height: 260px;\r\n\r\n          .personal-table-container {\r\n            height: 100%;\r\n            display: flex;\r\n            flex-direction: column;\r\n\r\n            .table-header {\r\n              display: flex;\r\n              background: rgba(31, 198, 255, 0.10);\r\n              border-radius: 4px 4px 0 0;\r\n              height: 40px;\r\n              align-items: center;\r\n\r\n              .header-cell {\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                color: #fff;\r\n                font-size: 14px;\r\n                font-weight: 500;\r\n\r\n                &.name-cell {\r\n                  flex: 1.2;\r\n                }\r\n\r\n                &.submit-cell,\r\n                &.adopt-cell {\r\n                  flex: 1;\r\n                }\r\n              }\r\n            }\r\n\r\n            .table-body {\r\n              flex: 1;\r\n              overflow-y: auto;\r\n\r\n              &::-webkit-scrollbar {\r\n                width: 4px;\r\n              }\r\n\r\n              &::-webkit-scrollbar-track {\r\n                background: rgba(255, 255, 255, 0.1);\r\n                border-radius: 2px;\r\n              }\r\n\r\n              &::-webkit-scrollbar-thumb {\r\n                background: rgba(31, 198, 255, 0.6);\r\n                border-radius: 2px;\r\n\r\n                &:hover {\r\n                  background: rgba(31, 198, 255, 0.8);\r\n                }\r\n              }\r\n\r\n              .table-row {\r\n                display: flex;\r\n                height: 50px;\r\n                align-items: center;\r\n                border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n\r\n                &:hover {\r\n                  background: rgba(31, 198, 255, 0.05);\r\n                }\r\n\r\n                .body-cell {\r\n                  display: flex;\r\n                  align-items: center;\r\n                  justify-content: center;\r\n\r\n                  &.name-cell {\r\n                    flex: 1.2;\r\n                    justify-content: flex-start;\r\n                    padding-left: 15px;\r\n\r\n                    .avatar-container {\r\n                      display: flex;\r\n                      align-items: center;\r\n\r\n                      .avatar {\r\n                        width: 32px;\r\n                        height: 32px;\r\n                        border-radius: 50%;\r\n                        margin-right: 10px;\r\n                        object-fit: cover;\r\n                        border: 2px solid rgba(31, 198, 255, 0.3);\r\n                      }\r\n\r\n                      .name {\r\n                        color: #fff;\r\n                        font-size: 14px;\r\n                      }\r\n                    }\r\n                  }\r\n\r\n                  &.submit-cell {\r\n                    flex: 1;\r\n\r\n                    .submit-count {\r\n                      color: #1FC6FF;\r\n                      font-size: 16px;\r\n                      font-weight: 600;\r\n                    }\r\n                  }\r\n\r\n                  &.adopt-cell {\r\n                    flex: 1;\r\n\r\n                    .adopt-count {\r\n                      color: #FFD600;\r\n                      font-size: 16px;\r\n                      font-weight: 600;\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .hot_word_analysis {\r\n        background: url('../../../assets/largeScreen/hot_word_analysis_bg3.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 310px;\r\n        width: 100%;\r\n\r\n        .hot_word_analysis_content {\r\n          height: calc(100% - 92px);\r\n          margin-top: 72px;\r\n          margin-bottom: 20px;\r\n          position: relative;\r\n        }\r\n      }\r\n    }\r\n\r\n    .center-panel {\r\n      flex: 1;\r\n      gap: 20px;\r\n      display: flex;\r\n      flex-direction: column;\r\n\r\n      .social_overall_situation {\r\n        background: url('../../../assets/largeScreen/social_overall_situation_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 460px;\r\n        width: 100%;\r\n\r\n        .social_overall_situation_content {\r\n          margin-top: 65px;\r\n          margin-left: 16px;\r\n          margin-right: 16px;\r\n\r\n        }\r\n      }\r\n\r\n      .each_unit_submits {\r\n        background: url('../../../assets/largeScreen/each_unit_submits_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 450px;\r\n        width: 100%;\r\n\r\n        .each_unit_submits_content {\r\n          height: 100%;\r\n          margin-top: 65px;\r\n          margin-left: 16px;\r\n          margin-right: 16px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}