{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\home\\homeBox.vue?vue&type=template&id=76285fab&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\home\\homeBox.vue", "mtime": 1756454249558}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1655712169000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}